package com.example.infomaint.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.DBRef;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "activities")
public class Activity {
    
    @Id
    private String id;
    
    @NotNull(message = "Activity type is required")
    private ActivityType type;
    
    @NotBlank(message = "Title is required")
    private String title;
    
    @NotBlank(message = "Description is required")
    private String description;
    
    @DBRef
    @NotNull(message = "User is required")
    private User user;
    
    @NotNull(message = "Start time is required")
    private LocalDateTime startTime;
    
    private LocalDateTime endTime;
    
    @Positive(message = "Duration must be positive")
    private Double durationHours;
    
    private String projectName;
    
    private String clientName;
    
    private String taskCategory;
    
    @NotNull(message = "Status is required")
    private ActivityStatus status = ActivityStatus.IN_PROGRESS;
    
    private String comments;
    
    private String location;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
    
    private String createdBy;
    
    private String updatedBy;
    
    // For billing purposes
    private Boolean billable = false;
    
    private Double hourlyRate;
    
    private Double totalCost;
    
    public enum ActivityType {
        SUPPORT, DEPLOYMENT, MONITORING, DEVELOPMENT, MEETING, TRAINING, MAINTENANCE, OTHER
    }
    
    public enum ActivityStatus {
        PLANNED, IN_PROGRESS, COMPLETED, CANCELLED, ON_HOLD
    }
    
    // Calculate duration when end time is set
    public void calculateDuration() {
        if (startTime != null && endTime != null) {
            long minutes = java.time.Duration.between(startTime, endTime).toMinutes();
            this.durationHours = minutes / 60.0;
        }
    }
    
    // Calculate total cost if billable
    public void calculateTotalCost() {
        if (billable && hourlyRate != null && durationHours != null) {
            this.totalCost = hourlyRate * durationHours;
        }
    }
}
