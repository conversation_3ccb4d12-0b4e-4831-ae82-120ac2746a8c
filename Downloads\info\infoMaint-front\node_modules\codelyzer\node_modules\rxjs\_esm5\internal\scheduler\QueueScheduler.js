/** PURE_IMPORTS_START tslib,_AsyncScheduler PURE_IMPORTS_END */
import * as tslib_1 from "tslib";
import { AsyncScheduler } from './AsyncScheduler';
var QueueScheduler = /*@__PURE__*/ (function (_super) {
    tslib_1.__extends(QueueScheduler, _super);
    function QueueScheduler() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    return QueueScheduler;
}(AsyncScheduler));
export { QueueScheduler };
//# sourceMappingURL=QueueScheduler.js.map
