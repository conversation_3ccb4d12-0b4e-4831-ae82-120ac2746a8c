/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng = global.ng || {};
    global.ng.common = global.ng.common || {};
    global.ng.common.locales = global.ng.common.locales || {};
    const u = undefined;
    function plural(val) {
const n = val;

if (n === 1)
    return 1;
return 5;
}
    global.ng.common.locales['so-et'] = ["so-ET",[["h","d"],["GH","GD"],u],[["GH","GD"],u,u],[["A","I","T","A","Kh","J","S"],["Axd","Isn","Tldo","Arbc","Khms","Jmc","<PERSON>bt<PERSON>"],["<PERSON>xa<PERSON>","<PERSON>ii<PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON>bt<PERSON>"],["Axd","Isn","Tldo","Arbc","Khms","Jmc","Sbti"]],u,[["J","F","M","A","M","J","L","O","S","O","N","D"],["<PERSON>","Feb","Mar","Abr","May","Jun","Lul","Ogs","Seb","Okt","Nof","<PERSON>s"],["Bisha Koobaad","Bisha Labaad","Bisha Saddexaad","Bisha Afraad","Bisha Shanaad","Bisha Lixaad","Bisha Todobaad","Bisha Sideedaad","Bisha Sagaalaad","Bisha Tobnaad","Bisha Kow iyo Tobnaad","Bisha Laba iyo Tobnaad"]],[["J","F","M","A","M","J","L","O","S","O","N","D"],["Jan","Feb","Mar","Abr","May","Jun","Lul","Ogs","Seb","Okt","Nof","Dis"],["Jannaayo","Febraayo","Maarso","Abriil","May","Juun","Luuliyo","Ogost","Sebtembar","Oktoobar","Nofembar","Desembar"]],[["B","A"],["CH","CD"],["Ciise Hortii","Ciise Dabadii"]],0,[6,0],["dd/MM/yy","dd-MMM-y","dd MMMM y","EEEE, MMMM dd, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1} {0}",u,u,u],[".",",",";","%","+","-","E","×","‰","∞","MaL",":"],["#,##0.###","#,##0%","¤#,##0.00","#E0"],"ETB","Br","Birta Itoobbiya",{"BBD":["DBB","$"],"ETB":["Br"],"JPY":["JP¥","¥"],"SOS":["S"],"USD":["US$","$"]},"ltr", plural, []];
  })(typeof globalThis !== 'undefined' && globalThis || typeof global !== 'undefined' && global || typeof window !== 'undefined' && window);
    