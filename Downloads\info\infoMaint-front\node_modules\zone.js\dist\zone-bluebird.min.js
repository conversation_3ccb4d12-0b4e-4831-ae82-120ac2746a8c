"use strict";
/**
 * @license Angular v14.2.0-next.0
 * (c) 2010-2022 Google LLC. https://angular.io/
 * License: MIT
 */!function(n){"function"==typeof define&&define.amd?define(n):n()}((function(){
/**
     * @license
     * Copyright Google LLC All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
Zone.__load_patch("bluebird",(function(n,e,o){e[e.__symbol__("bluebird")]=function t(i){["then","spread","finally"].forEach((function(n){o.patchMethod(i.prototype,n,(function(n){return function(o,t){for(var r=e.current,c=function(n){var e=t[n];"function"==typeof e&&(t[n]=function(){var n=this,o=arguments;return new i((function(t,i){r.scheduleMicroTask("Promise.then",(function(){try{t(e.apply(n,o))}catch(n){i(n)}}))}))})},d=0;d<t.length;d++)c(d);return n.apply(o,t)}}))})),"undefined"!=typeof window?window.addEventListener("unhandledrejection",(function(n){var e=n.detail&&n.detail.reason;e&&e.isHandledByZone&&(n.preventDefault(),"function"==typeof n.stopImmediatePropagation&&n.stopImmediatePropagation())})):"undefined"!=typeof process&&process.on("unhandledRejection",(function(n,e){if(n&&n.isHandledByZone){var o=process.listeners("unhandledRejection");o&&(process.removeAllListeners("unhandledRejection"),process.nextTick((function(){o.forEach((function(n){return process.on("unhandledRejection",n)}))})))}})),i.onPossiblyUnhandledRejection((function(n,t){try{e.current.runGuarded((function(){throw n.isHandledByZone=!0,n}))}catch(n){n.isHandledByZone=!1,o.onUnhandledError(n)}})),n.Promise=i}}))}));