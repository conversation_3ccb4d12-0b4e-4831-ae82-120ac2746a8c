package com.example.infomaint.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.DBRef;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "requests")
public class Request {
    
    @Id
    private String id;
    
    @NotNull(message = "Request type is required")
    private RequestType type;
    
    @NotBlank(message = "Title is required")
    private String title;
    
    @NotBlank(message = "Description is required")
    private String description;
    
    @NotNull(message = "Status is required")
    private RequestStatus status = RequestStatus.PENDING;
    
    @NotNull(message = "Priority is required")
    private Priority priority = Priority.MEDIUM;
    
    @DBRef
    @NotNull(message = "Requester is required")
    private User requester;
    
    @DBRef
    private User assignedTo;
    
    @DBRef
    private User approvedBy;
    
    private LocalDateTime requestDate;
    
    private LocalDateTime expectedDate;
    
    private LocalDateTime completedDate;
    
    private LocalDateTime approvedDate;
    
    private String approvalComments;
    
    private String completionComments;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
    
    private String createdBy;
    
    private String updatedBy;
    
    // For leave requests
    private LocalDateTime startDate;
    
    private LocalDateTime endDate;
    
    private Integer daysRequested;
    
    // For access requests
    private String systemName;
    
    private String accessLevel;
    
    // For intervention requests
    private String location;
    
    private String equipmentType;
    
    private String urgencyLevel;
    
    public enum RequestType {
        LEAVE, ACCESS, INTERVENTION, SUPPORT, OTHER
    }
    
    public enum RequestStatus {
        PENDING, IN_PROGRESS, APPROVED, REJECTED, COMPLETED, CANCELLED
    }
    
    public enum Priority {
        LOW, MEDIUM, HIGH, URGENT
    }
}
