/**
 * @license Angular v9.0.0
 * (c) 2010-2020 Google LLC. https://angular.io/
 * License: MIT
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("rxjs"),require("rxjs/operators")):"function"==typeof define&&define.amd?define("@angular/core",["exports","rxjs","rxjs/operators"],t):t(((e=e||self).ng=e.ng||{},e.ng.core={}),e.rxjs,e.rxjs.operators)}(this,(function(e,t,n){"use strict";
/*! *****************************************************************************
    Copyright (c) Microsoft Corporation. All rights reserved.
    Licensed under the Apache License, Version 2.0 (the "License"); you may not use
    this file except in compliance with the License. You may obtain a copy of the
    License at http://www.apache.org/licenses/LICENSE-2.0

    THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
    WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
    MERCHANTABLITY OR NON-INFRINGEMENT.

    See the Apache Version 2.0 License for specific language governing permissions
    and limitations under the License.
    ***************************************************************************** */var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)};function o(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var i=function(){return(i=Object.assign||function e(t){for(var n,r=1,o=arguments.length;r<o;r++)for(var i in n=arguments[r])Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i]);return t}).apply(this,arguments)};function a(e,t,n,r){var o,i=arguments.length,a=i<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,r);else for(var u=e.length-1;u>=0;u--)(o=e[u])&&(a=(i<3?o(a):i>3?o(t,n,a):o(t,n))||a);return i>3&&a&&Object.defineProperty(t,n,a),a}function u(e,t){return function(n,r){t(n,r,e)}}function s(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}function l(e){var t="function"==typeof Symbol&&e[Symbol.iterator],n=0;return t?t.call(e):{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}}}function c(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a}function f(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(c(arguments[t]));return e}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
var d="__annotations__",p="__parameters__",h="__prop__metadata__";function v(e,t,n,r,o){var i=y(t);function a(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];if(this instanceof a)return i.call.apply(i,f([this],t)),this;var u=new((e=a).bind.apply(e,f([void 0],t)));return function e(n){return o&&o.apply(void 0,f([n],t)),(n.hasOwnProperty(d)?n[d]:Object.defineProperty(n,d,{value:[]})[d]).push(u),r&&r(n),n}}return n&&(a.prototype=Object.create(n.prototype)),a.prototype.ngMetadataName=e,a.annotationCls=a,a}function y(e){return function t(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];if(e){var o=e.apply(void 0,f(n));for(var i in o)this[i]=o[i]}}}function g(e,t,n){var r=y(t);function o(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];if(this instanceof o)return r.apply(this,t),this;var i=new((e=o).bind.apply(e,f([void 0],t)));return a.annotation=i,a;function a(e,t,n){for(var r=e.hasOwnProperty(p)?e[p]:Object.defineProperty(e,p,{value:[]})[p];r.length<=n;)r.push(null);return(r[n]=r[n]||[]).push(i),e}}return n&&(o.prototype=Object.create(n.prototype)),o.prototype.ngMetadataName=e,o.annotationCls=o,o}function m(e,t,n,r){var o=y(t);function i(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];if(this instanceof i)return o.apply(this,t),this;var a=new((e=i).bind.apply(e,f([void 0],t)));return function u(e,n){var o=e.constructor,i=o.hasOwnProperty(h)?o[h]:Object.defineProperty(o,h,{value:{}})[h];i[n]=i.hasOwnProperty(n)&&i[n]||[],i[n].unshift(a),r&&r.apply(void 0,f([e,n],t))}}return n&&(i.prototype=Object.create(n.prototype)),i.prototype.ngMetadataName=e,i.annotationCls=i,i}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var _,w=function(e){return{token:e}},b=g("Inject",w),C=g("Optional"),I=g("Self"),x=g("SkipSelf"),E=g("Host"),k=function(e){return{attributeName:e}},O=g("Attribute",k);
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
function P(e){for(var t in e)if(e[t]===P)return t;throw Error("Could not find renamed property on target object.")}function N(e,t){for(var n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function T(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}(
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
_=e.InjectFlags||(e.InjectFlags={}))[_.Default=0]="Default",_[_.Host=1]="Host",_[_.Self=2]="Self",_[_.SkipSelf=4]="SkipSelf",_[_.Optional=8]="Optional";var j=T;function D(e){return{factory:e.factory,providers:e.providers||[],imports:e.imports||[]}}function A(e){return S(e,e[V])||S(e,e[H])}function S(e,t){return t&&t.token===e?t:null}function R(e){return e&&(e.hasOwnProperty(F)||e.hasOwnProperty(L))?e[F]:null}var V=P({"ɵprov":P}),F=P({"ɵinj":P}),M=P({"ɵprovFallback":P}),H=P({ngInjectableDef:P}),L=P({ngInjectorDef:P});
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
function B(e){if("string"==typeof e)return e;if(Array.isArray(e))return"["+e.map(B).join(", ")+"]";if(null==e)return""+e;if(e.overriddenName)return""+e.overriddenName;if(e.name)return""+e.name;var t=e.toString();if(null==t)return""+t;var n=t.indexOf("\n");return-1===n?t:t.substring(0,n)}function U(e,t){return null==e||""===e?null===t?"":t:null==t||""===t?e:e+" "+t}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var Q=P({__forward_ref__:P});function z(e){return e.__forward_ref__=z,e.toString=function(){return B(this())},e}function q(e){return Z(e)?e():e}function Z(e){return"function"==typeof e&&e.hasOwnProperty(Q)&&e.__forward_ref__===z}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var W,K,G,J="undefined"!=typeof globalThis&&globalThis,Y="undefined"!=typeof window&&window,$="undefined"!=typeof self&&"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&self,X="undefined"!=typeof global&&global,ee=J||X||Y||$;
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
function te(){var e=ee.ng;if(!e||!e.ɵcompilerFacade)throw new Error("Angular JIT compilation failed: '@angular/compiler' not loaded!\n  - JIT compilation is discouraged for production use-cases! Consider AOT mode instead.\n  - Did you bootstrap using '@angular/platform-browser-dynamic' or '@angular/platform-server'?\n  - Alternatively provide the compiler with 'import \"@angular/compiler\";' before bootstrapping.");return e.ɵcompilerFacade}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */!function(e){e[e.Token=0]="Token",e[e.Attribute=1]="Attribute",e[e.ChangeDetectorRef=2]="ChangeDetectorRef",e[e.Invalid=3]="Invalid"}(W||(W={})),function(e){e[e.Directive=0]="Directive",e[e.Component=1]="Component",e[e.Injectable=2]="Injectable",e[e.Pipe=3]="Pipe",e[e.NgModule=4]="NgModule"}(K||(K={})),function(e){e[e.Emulated=0]="Emulated",e[e.Native=1]="Native",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom"}(G||(G={}));var ne,re=P({"ɵcmp":P}),oe=P({"ɵdir":P}),ie=P({"ɵpipe":P}),ae=P({"ɵmod":P}),ue=P({"ɵloc":P}),se=P({"ɵfac":P}),le=P({__NG_ELEMENT_ID__:P}),ce=function(){function e(e,t){this._desc=e,this.ngMetadataName="InjectionToken",this.ɵprov=void 0,"number"==typeof t?this.__NG_ELEMENT_ID__=t:void 0!==t&&(this.ɵprov=T({token:this,providedIn:t.providedIn||"root",factory:t.factory}))}return e.prototype.toString=function(){return"InjectionToken "+this._desc},e}(),fe=new ce("INJECTOR",-1),de={},pe="ngTempTokenPath",he="ngTokenPath",ve=/\n/gm,ye="ɵ",ge="__source",me=P({provide:String,useValue:P}),_e=void 0;function we(e){var t=_e;return _e=e,t}function be(e){var t=ne;return ne=e,t}function Ce(t,n){if(void 0===n&&(n=e.InjectFlags.Default),void 0===_e)throw new Error("inject() must be called from an injection context");return null===_e?ke(t,void 0,n):_e.get(t,n&e.InjectFlags.Optional?null:void 0,n)}function Ie(t,n){return void 0===n&&(n=e.InjectFlags.Default),(ne||Ce)(q(t),n)}function xe(e){throw new Error("invalid")}var Ee=Ie;function ke(t,n,r){var o=A(t);if(o&&"root"==o.providedIn)return void 0===o.value?o.value=o.factory():o.value;if(r&e.InjectFlags.Optional)return null;if(void 0!==n)return n;throw new Error("Injector: NOT_FOUND ["+B(t)+"]")}function Oe(t){for(var n=[],r=0;r<t.length;r++){var o=q(t[r]);if(Array.isArray(o)){if(0===o.length)throw new Error("Arguments array must have arguments.");for(var i=void 0,a=e.InjectFlags.Default,u=0;u<o.length;u++){var s=o[u];s instanceof C||"Optional"===s.ngMetadataName||s===C?a|=e.InjectFlags.Optional:s instanceof x||"SkipSelf"===s.ngMetadataName||s===x?a|=e.InjectFlags.SkipSelf:s instanceof I||"Self"===s.ngMetadataName||s===I?a|=e.InjectFlags.Self:i=s instanceof b||s===b?s.token:s}n.push(Ie(i,a))}else n.push(Ie(o))}return n}var Pe=function(){function e(){}return e.prototype.get=function(e,t){if(void 0===t&&(t=de),t===de){var n=new Error("NullInjectorError: No provider for "+B(e)+"!");throw n.name="NullInjectorError",n}return t},e}();function Ne(e,t,n,r){var o=e[pe];throw t[ge]&&o.unshift(t[ge]),e.message=Te("\n"+e.message,o,n,r),e[he]=o,e[pe]=null,e}function Te(e,t,n,r){void 0===r&&(r=null),e=e&&"\n"===e.charAt(0)&&e.charAt(1)==ye?e.substr(2):e;var o=B(t);if(Array.isArray(t))o=t.map(B).join(" -> ");else if("object"==typeof t){var i=[];for(var a in t)if(t.hasOwnProperty(a)){var u=t[a];i.push(a+":"+("string"==typeof u?JSON.stringify(u):B(u)))}o="{"+i.join(", ")+"}"}return n+(r?"("+r+")":"")+"["+o+"]: "+e.replace(ve,"\n  ")}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var je,De,Ae={"ɵɵdefineInjectable":T,"ɵɵdefineInjector":D,"ɵɵinject":Ie,"ɵɵgetFactoryOf":function e(t){var n=t;if(Z(t))return function(){var t=e(q(n));return t?t():null};var r=A(n)||R(n);return r&&void 0!==r.factory?r.factory:null}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */,"ɵɵinvalidFactoryDep":xe},Se=function Se(){},Re=function Re(){};function Ve(e,t){null==e&&function n(e,t,r,o){throw new Error("ASSERTION ERROR: "+e+(null==o?"":" [Expected=> "+r+" "+o+" "+t+" <=Actual]"))}(t,e,null,"!=")}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
function Fe(e,t){for(var n=0;n<e.length;n++)t.push(e[n])}function Me(e,t){void 0===t&&(t=e);for(var n=0;n<e.length;n++){var r=e[n];Array.isArray(r)?(t===e&&(t=e.slice(0,n)),Me(r,t)):t!==e&&t.push(r)}return t}function He(e,t){e.forEach((function(e){return Array.isArray(e)?He(e,t):t(e)}))}function Le(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function Be(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function Ue(e,t){for(var n=[],r=0;r<e;r++)n.push(t);return n}function Qe(e,t,n){var r=qe(e,t);return r>=0?e[1|r]=n:function o(e,t,n,r){var o=e.length;if(o==t)e.push(n,r);else if(1===o)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;)e[o]=e[o-2],o--;e[t]=n,e[t+1]=r}}(e,r=~r,t,n),r}function ze(e,t){var n=qe(e,t);if(n>=0)return e[1|n]}function qe(e,t){return function n(e,t,r){for(var o=0,i=e.length>>r;i!==o;){var a=o+(i-o>>1),u=e[a<<r];if(t===u)return a<<r;u>t?i=a:o=a+1}return~(i<<r)}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */(e,t,1)}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
function Ze(e){return""+{toString:e}}
/**
    * @license
    * Copyright Google Inc. All Rights Reserved.
    *
    * Use of this source code is governed by an MIT-style license that can be
    * found in the LICENSE file at https://angular.io/license
    */(je=e.ChangeDetectionStrategy||(e.ChangeDetectionStrategy={}))[je.OnPush=0]="OnPush",je[je.Default=1]="Default",(De=e.ɵChangeDetectorStatus||(e.ɵChangeDetectorStatus={}))[De.CheckOnce=0]="CheckOnce",De[De.Checked=1]="Checked",De[De.CheckAlways=2]="CheckAlways",De[De.Detached=3]="Detached",De[De.Errored=4]="Errored",De[De.Destroyed=5]="Destroyed",
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
function(e){e[e.Emulated=0]="Emulated",e[e.Native=1]="Native",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom"}(e.ViewEncapsulation||(e.ViewEncapsulation={}));var We={},Ke=[],Ge=0;function Je(t){var n=t.type,r=n.prototype,o={},i={type:n,providersResolver:null,decls:t.decls,vars:t.vars,factory:null,template:t.template||null,consts:t.consts||null,ngContentSelectors:t.ngContentSelectors,hostBindings:t.hostBindings||null,hostVars:t.hostVars||0,hostAttrs:t.hostAttrs||null,contentQueries:t.contentQueries||null,declaredInputs:o,inputs:null,outputs:null,exportAs:t.exportAs||null,onChanges:null,onInit:r.ngOnInit||null,doCheck:r.ngDoCheck||null,afterContentInit:r.ngAfterContentInit||null,afterContentChecked:r.ngAfterContentChecked||null,afterViewInit:r.ngAfterViewInit||null,afterViewChecked:r.ngAfterViewChecked||null,onDestroy:r.ngOnDestroy||null,onPush:t.changeDetection===e.ChangeDetectionStrategy.OnPush,directiveDefs:null,pipeDefs:null,selectors:t.selectors||Ke,viewQuery:t.viewQuery||null,features:t.features||null,data:t.data||{},encapsulation:t.encapsulation||e.ViewEncapsulation.Emulated,id:"c",styles:t.styles||Ke,_:null,setInput:null,schemas:t.schemas||null,tView:null};return i._=Ze((function(){var e=t.directives,n=t.features,r=t.pipes;i.id+=Ge++,i.inputs=rt(t.inputs,o),i.outputs=rt(t.outputs),n&&n.forEach((function(e){return e(i)})),i.directiveDefs=e?function(){return("function"==typeof e?e():e).map($e)}:null,i.pipeDefs=r?function(){return("function"==typeof r?r():r).map(Xe)}:null})),i}function Ye(e,t,n){var r=e.ɵcmp;r.directiveDefs=function(){return t.map($e)},r.pipeDefs=function(){return n.map(Xe)}}function $e(e){return at(e)||ut(e)}function Xe(e){return st(e)}var et={};function tt(e){var t={type:e.type,bootstrap:e.bootstrap||Ke,declarations:e.declarations||Ke,imports:e.imports||Ke,exports:e.exports||Ke,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null};return null!=e.id&&Ze((function(){et[e.id]=e.type})),t}function nt(e,t){return Ze((function(){var n=ct(e,!0);n.declarations=t.declarations||Ke,n.imports=t.imports||Ke,n.exports=t.exports||Ke}))}function rt(e,t){if(null==e)return We;var n={};for(var r in e)if(e.hasOwnProperty(r)){var o=e[r],i=o;Array.isArray(o)&&(i=o[1],o=o[0]),n[o]=r,t&&(t[o]=i)}return n}var ot=Je;function it(e){return{type:e.type,name:e.name,factory:null,pure:!1!==e.pure,onDestroy:e.type.prototype.ngOnDestroy||null}}function at(e){return e[re]||null}function ut(e){return e[oe]||null}function st(e){return e[ie]||null}function lt(e,t){return e.hasOwnProperty(se)?e[se]:null}function ct(e,t){var n=e[ae]||null;if(!n&&!0===t)throw new Error("Type "+B(e)+" does not have 'ɵmod' property.");return n}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
var ft=0,dt=1,pt=2,ht=3,vt=4,yt=5,gt=6,mt=7,_t=8,wt=9,bt=10,Ct=11,It=12,xt=13,Et=14,kt=15,Ot=16,Pt=17,Nt=18,Tt=19,jt=1,Dt=2,At=5,St=7,Rt=8,Vt=9;
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
function Ft(e){return Array.isArray(e)&&"object"==typeof e[jt]}function Mt(e){return Array.isArray(e)&&!0===e[jt]}function Ht(e){return 0!=(8&e.flags)}function Lt(e){return 2==(2&e.flags)}function Bt(e){return 1==(1&e.flags)}function Ut(e){return null!==e.template}function Qt(e){return 0!=(512&e[pt])}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
var zt={lFrame:yn(null),bindingsEnabled:!0,checkNoChangesMode:!1};function qt(){return zt.bindingsEnabled}function Zt(){zt.bindingsEnabled=!0}function Wt(){zt.bindingsEnabled=!1}function Kt(){return zt.lFrame.lView}function Gt(){return zt.lFrame.tView}function Jt(e){zt.lFrame.contextLView=e}function Yt(){return zt.lFrame.previousOrParentTNode}function $t(e,t){zt.lFrame.previousOrParentTNode=e,zt.lFrame.isParent=t}function Xt(){return zt.lFrame.isParent}function en(){zt.lFrame.isParent=!1}function tn(){return zt.checkNoChangesMode}function nn(e){zt.checkNoChangesMode=e}function rn(){var e=zt.lFrame,t=e.bindingRootIndex;return-1===t&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function on(){return zt.lFrame.bindingIndex}function an(e){return zt.lFrame.bindingIndex=e}function un(){return zt.lFrame.bindingIndex++}function sn(e){var t=zt.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function ln(e,t){var n=zt.lFrame;n.bindingIndex=n.bindingRootIndex=e,n.currentDirectiveIndex=t}function cn(){return zt.lFrame.currentQueryIndex}function fn(e){zt.lFrame.currentQueryIndex=e}function dn(e,t){var n=vn();zt.lFrame=n,n.previousOrParentTNode=t,n.lView=e}var pn=gn;function hn(e,t){var n=vn(),r=e[dt];zt.lFrame=n,n.previousOrParentTNode=t,n.isParent=!0,n.lView=e,n.tView=r,n.selectedIndex=0,n.contextLView=e,n.elementDepthCount=0,n.currentDirectiveIndex=-1,n.currentNamespace=null,n.currentSanitizer=null,n.bindingRootIndex=-1,n.bindingIndex=r.bindingStartIndex,n.currentQueryIndex=0}function vn(){var e=zt.lFrame,t=null===e?null:e.child;return null===t?yn(e):t}function yn(e){var t={previousOrParentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:0,contextLView:null,elementDepthCount:0,currentNamespace:null,currentSanitizer:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null};return null!==e&&(e.child=t),t}function gn(){zt.lFrame=zt.lFrame.parent}function mn(e){return(zt.lFrame.contextLView=function t(e,n){for(;e>0;)n=n[kt],e--;return n}(e,zt.lFrame.contextLView))[_t]}function _n(){return zt.lFrame.selectedIndex}function wn(e){zt.lFrame.selectedIndex=e}function bn(){zt.lFrame.currentNamespace="http://www.w3.org/2000/svg"}function Cn(){zt.lFrame.currentNamespace="http://www.w3.org/1998/MathML/"}function In(){!function e(){zt.lFrame.currentNamespace=null}()}function xn(e,t){for(var n=t.directiveStart,r=t.directiveEnd;n<r;n++){var o=e.data[n];o.afterContentInit&&(e.contentHooks||(e.contentHooks=[])).push(-n,o.afterContentInit),o.afterContentChecked&&((e.contentHooks||(e.contentHooks=[])).push(n,o.afterContentChecked),(e.contentCheckHooks||(e.contentCheckHooks=[])).push(n,o.afterContentChecked)),o.afterViewInit&&(e.viewHooks||(e.viewHooks=[])).push(-n,o.afterViewInit),o.afterViewChecked&&((e.viewHooks||(e.viewHooks=[])).push(n,o.afterViewChecked),(e.viewCheckHooks||(e.viewCheckHooks=[])).push(n,o.afterViewChecked)),null!=o.onDestroy&&(e.destroyHooks||(e.destroyHooks=[])).push(n,o.onDestroy)}}function En(e,t,n){Pn(e,t,3,n)}function kn(e,t,n,r){(3&e[pt])===n&&Pn(e,t,n,r)}function On(e,t){var n=e[pt];(3&n)===t&&(n&=1023,e[pt]=n+=1)}function Pn(e,t,n,r){for(var o=null!=r?r:-1,i=0,a=void 0!==r?65535&e[Nt]:0;a<t.length;a++)if("number"==typeof t[a+1]){if(i=t[a],null!=r&&i>=r)break}else t[a]<0&&(e[Nt]+=65536),(i<o||-1==o)&&(Nn(e,n,t,a),e[Nt]=(**********&e[Nt])+a+2),a++}function Nn(e,t,n,r){var o=n[r]<0,i=n[r+1],a=e[o?-n[r]:n[r]];o?e[pt]>>10<e[Nt]>>16&&(3&e[pt])===t&&(e[pt]+=1024,i.call(a)):i.call(a)}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var Tn,jn=8,Dn=8,An=9,Sn=-1,Rn=function Rn(e,t,n){this.factory=e,this.resolving=!1,this.canSeeViewProviders=t,this.injectImpl=n},Vn=void 0;function Fn(){return void 0!==Vn?Vn:"undefined"!=typeof document?document:void 0}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function Mn(e){return!!e.listen}!function(e){e[e.Important=1]="Important",e[e.DashCase=2]="DashCase"}(Tn||(Tn={}));var Hn={createRenderer:function(e,t){return Fn()}};function Ln(e,t,n){for(var r=Mn(e),o=0;o<n.length;){var i=n[o];if("number"==typeof i){if(0!==i)break;o++;var a=n[o++],u=n[o++],s=n[o++];r?e.setAttribute(t,u,s,a):t.setAttributeNS(a,u,s)}else s=n[++o],Un(u=i)?r&&e.setProperty(t,u,s):r?e.setAttribute(t,u,s):t.setAttribute(u,s),o++}return o}function Bn(e){return 3===e||4===e||6===e}function Un(e){return 64===e.charCodeAt(0)}function Qn(e,t){if(null===t||0===t.length);else if(null===e||0===e.length)e=t.slice();else for(var n=-1,r=0;r<t.length;r++){var o=t[r];"number"==typeof o?n=o:0===n||zn(e,n,o,null,-1===n||2===n?t[++r]:null)}return e}function zn(e,t,n,r,o){var i=0,a=e.length;if(-1===t)a=-1;else for(;i<e.length;){var u=e[i++];if("number"==typeof u){if(u===t){a=-1;break}if(u>t){a=i-1;break}}}for(;i<e.length;){var s=e[i];if("number"==typeof s)break;if(s===n){if(null===r)return void(null!==o&&(e[i+1]=o));if(r===e[i+1])return void(e[i+2]=o)}i++,null!==r&&i++,null!==o&&i++}-1!==a&&(e.splice(a,0,t),i=a+1),e.splice(i++,0,n),null!==r&&e.splice(i++,0,r),null!==o&&e.splice(i++,0,o)}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function qn(e){return e!==Sn}function Zn(e){return 32767&e}function Wn(e){return e>>16}function Kn(e,t){for(var n=Wn(e),r=t;n>0;)r=r[kt],n--;return r}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function Gn(e){return"string"==typeof e?e:null==e?"":""+e}function Jn(e){return"function"==typeof e?e.name||e.toString():"object"==typeof e&&null!=e&&"function"==typeof e.type?e.type.name||e.type.toString():Gn(e)}var Yn=("undefined"!=typeof requestAnimationFrame&&requestAnimationFrame||setTimeout).bind(ee);function $n(e){return{name:"window",target:e.ownerDocument.defaultView}}function Xn(e){return{name:"document",target:e.ownerDocument}}function er(e){return{name:"body",target:e.ownerDocument.body}}function tr(e){return e instanceof Function?e():e}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var nr=!0;function rr(e){var t=nr;return nr=e,t}var or=255,ir=0;function ar(e,t){var n=sr(e,t);if(-1!==n)return n;var r=t[dt];r.firstCreatePass&&(e.injectorIndex=t.length,ur(r.data,e),ur(t,null),ur(r.blueprint,null));var o=lr(e,t),i=e.injectorIndex;if(qn(o))for(var a=Zn(o),u=Kn(o,t),s=u[dt].data,l=0;l<8;l++)t[i+l]=u[a+l]|s[a+l];return t[i+Dn]=o,i}function ur(e,t){e.push(0,0,0,0,0,0,0,0,t)}function sr(e,t){return-1===e.injectorIndex||e.parent&&e.parent.injectorIndex===e.injectorIndex||null==t[e.injectorIndex+Dn]?-1:e.injectorIndex}function lr(e,t){if(e.parent&&-1!==e.parent.injectorIndex)return e.parent.injectorIndex;for(var n=t[gt],r=1;n&&-1===n.injectorIndex;)n=(t=t[kt])?t[gt]:null,r++;return n?n.injectorIndex|r<<16:-1}function cr(e,t,n){!function r(e,t,n){var r="string"!=typeof n?n[le]:n.charCodeAt(0)||0;null==r&&(r=n[le]=ir++);var o=r&or,i=1<<o,a=64&o,u=32&o,s=t.data;128&o?a?u?s[e+7]|=i:s[e+6]|=i:u?s[e+5]|=i:s[e+4]|=i:a?u?s[e+3]|=i:s[e+2]|=i:u?s[e+1]|=i:s[e]|=i}(e,t,n)}function fr(e,t){if("class"===t)return e.classes;if("style"===t)return e.styles;var n=e.attrs;if(n)for(var r=n.length,o=0;o<r;){var i=n[o];if(Bn(i))break;if(0===i)o+=2;else if("number"==typeof i)for(o++;o<r&&"string"==typeof n[o];)o++;else{if(i===t)return n[o+1];o+=2}}return null}function dr(t,n,r,o,i){if(void 0===o&&(o=e.InjectFlags.Default),null!==t){var a=function u(e){if("string"==typeof e)return e.charCodeAt(0)||0;var t=e[le];return"number"==typeof t&&t>0?t&or:t}(r);if("function"==typeof a){dn(n,t);try{var s=a();if(null!=s||o&e.InjectFlags.Optional)return s;throw new Error("No provider for "+Jn(r)+"!")}finally{pn()}}else if("number"==typeof a){if(-1===a)return new _r(t,n);var l=null,c=sr(t,n),f=Sn,d=o&e.InjectFlags.Host?n[Ot][gt]:null;for((-1===c||o&e.InjectFlags.SkipSelf)&&(f=-1===c?lr(t,n):n[c+Dn],mr(o,!1)?(l=n[dt],c=Zn(f),n=Kn(f,n)):c=-1);-1!==c;){f=n[c+Dn];var p=n[dt];if(gr(a,c,p.data)){var h=hr(c,n,r,l,o,d);if(h!==pr)return h}mr(o,n[dt].data[c+jn]===d)&&gr(a,c,n)?(l=p,c=Zn(f),n=Kn(f,n)):c=-1}}}if(o&e.InjectFlags.Optional&&void 0===i&&(i=null),0==(o&(e.InjectFlags.Self|e.InjectFlags.Host))){var v=n[wt],y=be(void 0);try{return v?v.get(r,i,o&e.InjectFlags.Optional):ke(r,i,o&e.InjectFlags.Optional)}finally{be(y)}}if(o&e.InjectFlags.Optional)return i;throw new Error("NodeInjector: NOT_FOUND ["+Jn(r)+"]")}var pr={};function hr(t,n,r,o,i,a){var u=n[dt],s=u.data[t+jn],l=vr(s,u,r,null==o?Lt(s)&&nr:o!=u&&3===s.type,i&e.InjectFlags.Host&&a===s);return null!==l?yr(n,u,l,s):pr}function vr(e,t,n,r,o){for(var i=e.providerIndexes,a=t.data,u=65535&i,s=e.directiveStart,l=i>>16,c=o?u+l:e.directiveEnd,f=r?u:u+l;f<c;f++){var d=a[f];if(f<s&&n===d||f>=s&&d.type===n)return f}if(o){var p=a[s];if(p&&Ut(p)&&p.type===n)return s}return null}function yr(e,t,n,r){var o=e[n],i=t.data;if(function a(e){return e instanceof Rn}(o)){var u=o;if(u.resolving)throw new Error("Circular dep for "+Jn(i[n]));var s=rr(u.canSeeViewProviders);u.resolving=!0;var l=void 0;u.injectImpl&&(l=be(u.injectImpl)),dn(e,r);try{o=e[n]=u.factory(void 0,i,e,r),t.firstCreatePass&&n>=r.directiveStart&&
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
function c(e,t,n){var r=t.onChanges,o=t.onInit,i=t.doCheck;r&&((n.preOrderHooks||(n.preOrderHooks=[])).push(e,r),(n.preOrderCheckHooks||(n.preOrderCheckHooks=[])).push(e,r)),o&&(n.preOrderHooks||(n.preOrderHooks=[])).push(-e,o),i&&((n.preOrderHooks||(n.preOrderHooks=[])).push(e,i),(n.preOrderCheckHooks||(n.preOrderCheckHooks=[])).push(e,i))}(n,i[n],t)}finally{u.injectImpl&&be(l),rr(s),u.resolving=!1,pn()}}return o}function gr(e,t,n){var r=64&e,o=32&e;return!!((128&e?r?o?n[t+7]:n[t+6]:o?n[t+5]:n[t+4]:r?o?n[t+3]:n[t+2]:o?n[t+1]:n[t])&1<<e)}function mr(t,n){return!(t&e.InjectFlags.Self||t&e.InjectFlags.Host&&n)}var _r=function(){function e(e,t){this._tNode=e,this._lView=t}return e.prototype.get=function(e,t){return dr(this._tNode,this._lView,e,void 0,t)},e}();function wr(e){var t=e;if(Z(e))return function(){var e=wr(q(t));return e?e():null};var n=lt(t);if(null===n){var r=R(t);n=r&&r.factory}return n||null}function br(e){var t=Object.getPrototypeOf(e.prototype).constructor,n=t[se]||wr(t);return null!==n?n:function(e){return new e}}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var Cr="ngDebugContext",Ir="ngOriginalError",xr="ngErrorLogger";function Er(e){return e[Cr]}function kr(e){return e[Ir]}function Or(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];e.error.apply(e,f(t))}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var Pr=function(){function e(){this._console=console}return e.prototype.handleError=function(e){var t=this._findOriginalError(e),n=this._findContext(e),r=function o(e){return e[xr]||Or}(e);r(this._console,"ERROR",e),t&&r(this._console,"ORIGINAL ERROR",t),n&&r(this._console,"ERROR CONTEXT",n)},e.prototype._findContext=function(e){return e?Er(e)?Er(e):this._findContext(kr(e)):null},e.prototype._findOriginalError=function(e){for(var t=kr(e);t&&kr(t);)t=kr(t);return t},e}(),Nr=function(){function e(e){this.changingThisBreaksApplicationSecurity=e}return e.prototype.toString=function(){return"SafeValue must use [property]=binding: "+this.changingThisBreaksApplicationSecurity+" (see http://g.co/ng/security#xss)"},e}(),Tr=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t.prototype.getTypeName=function(){return"HTML"},t}(Nr),jr=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t.prototype.getTypeName=function(){return"Style"},t}(Nr),Dr=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t.prototype.getTypeName=function(){return"Script"},t}(Nr),Ar=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t.prototype.getTypeName=function(){return"URL"},t}(Nr),Sr=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t.prototype.getTypeName=function(){return"ResourceURL"},t}(Nr);
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function Rr(e){return e instanceof Nr?e.changingThisBreaksApplicationSecurity:e}function Vr(e,t){var n=Fr(e);if(null!=n&&n!==t){if("ResourceURL"===n&&"URL"===t)return!0;throw new Error("Required a safe "+t+", got a "+n+" (see http://g.co/ng/security#xss)")}return n===t}function Fr(e){return e instanceof Nr&&e.getTypeName()||null}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
var Mr=!0,Hr=!1;function Lr(){return Hr=!0,Mr}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
var Br=function(){function e(e){this.defaultDoc=e,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert");var t=this.inertDocument.body;if(null==t){var n=this.inertDocument.createElement("html");this.inertDocument.appendChild(n),t=this.inertDocument.createElement("body"),n.appendChild(t)}t.innerHTML='<svg><g onload="this.parentNode.remove()"></g></svg>',!t.querySelector||t.querySelector("svg")?(t.innerHTML='<svg><p><style><img src="</style><img src=x onerror=alert(1)//">',this.getInertBodyElement=t.querySelector&&t.querySelector("svg img")&&function r(){try{return!!window.DOMParser}catch(e){return!1}}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */()?this.getInertBodyElement_DOMParser:this.getInertBodyElement_InertDocument):this.getInertBodyElement=this.getInertBodyElement_XHR}return e.prototype.getInertBodyElement_XHR=function(e){e="<body><remove></remove>"+e+"</body>";try{e=encodeURI(e)}catch(e){return null}var t=new XMLHttpRequest;t.responseType="document",t.open("GET","data:text/html;charset=utf-8,"+e,!1),t.send(void 0);var n=t.response.body;return n.removeChild(n.firstChild),n},e.prototype.getInertBodyElement_DOMParser=function(e){e="<body><remove></remove>"+e+"</body>";try{var t=(new window.DOMParser).parseFromString(e,"text/html").body;return t.removeChild(t.firstChild),t}catch(e){return null}},e.prototype.getInertBodyElement_InertDocument=function(e){var t=this.inertDocument.createElement("template");if("content"in t)return t.innerHTML=e,t;var n=this.inertDocument.createElement("body");return n.innerHTML=e,this.defaultDoc.documentMode&&this.stripCustomNsAttrs(n),n},e.prototype.stripCustomNsAttrs=function(e){for(var t=e.attributes,n=t.length-1;0<n;n--){var r=t.item(n).name;"xmlns:ns1"!==r&&0!==r.indexOf("ns1:")||e.removeAttribute(r)}for(var o=e.firstChild;o;)o.nodeType===Node.ELEMENT_NODE&&this.stripCustomNsAttrs(o),o=o.nextSibling},e}(),Ur=/^(?:(?:https?|mailto|ftp|tel|file):|[^&:/?#]*(?:[/?#]|$))/gi,Qr=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[a-z0-9+\/]+=*$/i;function zr(e){return(e=String(e)).match(Ur)||e.match(Qr)?e:(Lr()&&console.warn("WARNING: sanitizing unsafe URL value "+e+" (see http://g.co/ng/security#xss)"),"unsafe:"+e)}function qr(e){return(e=String(e)).split(",").map((function(e){return zr(e.trim())})).join(", ")}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function Zr(e){var t,n,r={};try{for(var o=l(e.split(",")),i=o.next();!i.done;i=o.next())r[i.value]=!0}catch(e){t={error:e}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(t)throw t.error}}return r}function Wr(){for(var e,t,n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var o={};try{for(var i=l(n),a=i.next();!a.done;a=i.next()){var u=a.value;for(var s in u)u.hasOwnProperty(s)&&(o[s]=!0)}}catch(t){e={error:t}}finally{try{a&&!a.done&&(t=i.return)&&t.call(i)}finally{if(e)throw e.error}}return o}var Kr,Gr,Jr=Zr("area,br,col,hr,img,wbr"),Yr=Zr("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),$r=Zr("rp,rt"),Xr=Wr($r,Yr),eo=Wr(Yr,Zr("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),to=Wr($r,Zr("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),no=Wr(Jr,eo,to,Xr),ro=Zr("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),oo=Zr("srcset"),io=Zr("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),ao=Zr("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),uo=Wr(ro,oo,io,ao),so=Zr("script,style,template"),lo=function(){function e(){this.sanitizedSomething=!1,this.buf=[]}return e.prototype.sanitizeChildren=function(e){for(var t=e.firstChild,n=!0;t;)if(t.nodeType===Node.ELEMENT_NODE?n=this.startElement(t):t.nodeType===Node.TEXT_NODE?this.chars(t.nodeValue):this.sanitizedSomething=!0,n&&t.firstChild)t=t.firstChild;else for(;t;){t.nodeType===Node.ELEMENT_NODE&&this.endElement(t);var r=this.checkClobberedElement(t,t.nextSibling);if(r){t=r;break}t=this.checkClobberedElement(t,t.parentNode)}return this.buf.join("")},e.prototype.startElement=function(e){var t=e.nodeName.toLowerCase();if(!no.hasOwnProperty(t))return this.sanitizedSomething=!0,!so.hasOwnProperty(t);this.buf.push("<"),this.buf.push(t);for(var n=e.attributes,r=0;r<n.length;r++){var o=n.item(r),i=o.name,a=i.toLowerCase();if(uo.hasOwnProperty(a)){var u=o.value;ro[a]&&(u=zr(u)),oo[a]&&(u=qr(u)),this.buf.push(" ",i,'="',po(u),'"')}else this.sanitizedSomething=!0}return this.buf.push(">"),!0},e.prototype.endElement=function(e){var t=e.nodeName.toLowerCase();no.hasOwnProperty(t)&&!Jr.hasOwnProperty(t)&&(this.buf.push("</"),this.buf.push(t),this.buf.push(">"))},e.prototype.chars=function(e){this.buf.push(po(e))},e.prototype.checkClobberedElement=function(e,t){if(t&&(e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)===Node.DOCUMENT_POSITION_CONTAINED_BY)throw new Error("Failed to sanitize html because the element is clobbered: "+e.outerHTML);return t},e}(),co=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,fo=/([^\#-~ |!])/g;function po(e){return e.replace(/&/g,"&amp;").replace(co,(function(e){return"&#"+(1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320)+65536)+";"})).replace(fo,(function(e){return"&#"+e.charCodeAt(0)+";"})).replace(/</g,"&lt;").replace(/>/g,"&gt;")}function ho(e,t){var n=null;try{Kr=Kr||new Br(e);var r=t?String(t):"";n=Kr.getInertBodyElement(r);var o=5,i=r;do{if(0===o)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=n.innerHTML,n=Kr.getInertBodyElement(r)}while(r!==i);var a=new lo,u=a.sanitizeChildren(vo(n)||n);return Lr()&&a.sanitizedSomething&&console.warn("WARNING: sanitizing HTML stripped some content, see http://g.co/ng/security#xss"),u}finally{if(n)for(var s=vo(n)||n;s.firstChild;)s.removeChild(s.firstChild)}}function vo(e){return"content"in e&&function t(e){return e.nodeType===Node.ELEMENT_NODE&&"TEMPLATE"===e.nodeName}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */(e)?e.content:null}(Gr=e.SecurityContext||(e.SecurityContext={}))[Gr.NONE=0]="NONE",Gr[Gr.HTML=1]="HTML",Gr[Gr.STYLE=2]="STYLE",Gr[Gr.SCRIPT=3]="SCRIPT",Gr[Gr.URL=4]="URL",Gr[Gr.RESOURCE_URL=5]="RESOURCE_URL";
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
var yo=new RegExp("^([-,.\"'%_!# a-zA-Z0-9]+|(?:(?:matrix|translate|scale|rotate|skew|perspective)(?:X|Y|Z|3d)?|(?:rgb|hsl)a?|(?:repeating-)?(?:linear|radial)-gradient|(?:attr|calc|var))\\([-0-9.%, #a-zA-Z]+\\))$","g"),go=/^url\(([^)]+)\)$/;function mo(e){if(!(e=String(e).trim()))return"";var t=e.match(go);return t&&zr(t[1])===t[1]||e.match(yo)&&function n(e){for(var t=!0,n=!0,r=0;r<e.length;r++){var o=e.charAt(r);"'"===o&&n?t=!t:'"'===o&&t&&(n=!n)}return t&&n}(e)?e:(Lr()&&console.warn("WARNING: sanitizing unsafe style value "+e+" (see http://g.co/ng/security#xss)."),"unsafe")}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function _o(t){var n=Po();return n?n.sanitize(e.SecurityContext.HTML,t)||"":Vr(t,"HTML")?Rr(t):ho(Fn(),Gn(t))}function wo(t){var n=Po();return n?n.sanitize(e.SecurityContext.STYLE,t)||"":Vr(t,"Style")?Rr(t):mo(Gn(t))}function bo(t){var n=Po();return n?n.sanitize(e.SecurityContext.URL,t)||"":Vr(t,"URL")?Rr(t):zr(Gn(t))}function Co(t){var n=Po();if(n)return n.sanitize(e.SecurityContext.RESOURCE_URL,t)||"";if(Vr(t,"ResourceURL"))return Rr(t);throw new Error("unsafe value used in a resource URL context (see http://g.co/ng/security#xss)")}function Io(t){var n=Po();if(n)return n.sanitize(e.SecurityContext.SCRIPT,t)||"";if(Vr(t,"Script"))return Rr(t);throw new Error("unsafe value used in a script context")}function xo(e,t){return"src"===t&&("embed"===e||"frame"===e||"iframe"===e||"media"===e||"script"===e)||"href"===t&&("base"===e||"link"===e)?Co:bo}function Eo(e,t,n){return xo(t,n)(e)}var ko=function(e,t,n){if(void 0===t&&void 0===n)return wo(e);var r=!0;return 1&(n=n||3)&&(r=Oo(e)),2&n?r?wo(t):Rr(t):r};function Oo(e){return"background-image"===e||"backgroundImage"===e||"background"===e||"border-image"===e||"borderImage"===e||"border-image-source"===e||"borderImageSource"===e||"filter"===e||"list-style"===e||"listStyle"===e||"list-style-image"===e||"listStyleImage"===e||"clip-path"===e||"clipPath"===e}function Po(){var e=Kt();return e&&e[It]}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var No=/([A-Z])/g;function To(e){try{return null!=e?e.toString().slice(0,30):e}catch(e){return"[ERROR] Exception while trying to serialize the value"}}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var jo="__ngContext__";
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function Do(e){for(;Array.isArray(e);)e=e[ft];return e}function Ao(e,t){return Do(t[e+Tt])}function So(e,t){return Do(t[e.index])}function Ro(e,t){var n=e.index;return-1!==n?Do(t[n]):null}function Vo(e,t){return e.data[t+Tt]}function Fo(e,t){return e[t+Tt]}function Mo(e,t){var n=t[e];return Ft(n)?n:n[ft]}function Ho(e){return e[jo]||null}function Lo(e){var t=Ho(e);return t?Array.isArray(t)?t:t.lView:null}function Bo(e){return 4==(4&e[pt])}function Uo(e){return 128==(128&e[pt])}function Qo(e,t){return null===e||null==t?null:e[t]}function zo(e){e[Nt]=0}function qo(e){return e[Dt]>>1}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
function Zo(e){var t=Ho(e);if(t){if(Array.isArray(t)){var n=t,r=void 0,o=void 0,i=void 0;if(function a(e){return e&&e.constructor&&e.constructor.ɵcmp}(e)){if(-1==(r=$o(n,e)))throw new Error("The provided component was not found in the application");o=e}else if(function u(e){return e&&e.constructor&&e.constructor.ɵdir}(e)){if(-1==(r=function s(e,t){for(var n=e[dt].firstChild;n;){for(var r=n.directiveEnd,o=n.directiveStart;o<r;o++)if(e[o]===t)return n.index;n=Yo(n)}return-1}(n,e)))throw new Error("The provided directive was not found in the application");i=Xo(r,n,!1)}else if(-1==(r=Jo(n,e)))return null;var l=Ho(y=Do(n[r])),c=l&&!Array.isArray(l)?l:Wo(n,r,y);if(o&&void 0===c.component&&(c.component=o,Go(c.component,c)),i&&void 0===c.directives){c.directives=i;for(var f=0;f<i.length;f++)Go(i[f],c)}Go(c.native,c),t=c}}else for(var d=e,p=d;p=p.parentNode;){var h=Ho(p);if(h){if(n=void 0,!(n=Array.isArray(h)?h:h.lView))return null;var v=Jo(n,d);if(v>=0){var y;Go(y=Do(n[v]),c=Wo(n,v,y)),t=c;break}}}return t||null}function Wo(e,t,n){return{lView:e,nodeIndex:t,native:n,component:void 0,directives:void 0,localRefs:void 0}}function Ko(e){var t,n=Ho(e);if(Array.isArray(n)){var r=$o(n,e);(o=Wo(n,r,(t=Mo(r,n))[ft])).component=e,Go(e,o),Go(o.native,o)}else{var o;t=Mo((o=n).nodeIndex,o.lView)}return t}function Go(e,t){e[jo]=t}function Jo(e,t){for(var n=e[dt].firstChild;n;){if(Ro(n,e)===t)return n.index;n=Yo(n)}return-1}function Yo(e){if(e.child)return e.child;if(e.next)return e.next;for(;e.parent&&!e.parent.next;)e=e.parent;return e.parent&&e.parent.next}function $o(e,t){var n=e[dt].components;if(n)for(var r=0;r<n.length;r++){var o=n[r];if(Mo(o,e)[_t]===t)return o}else if(Mo(Tt,e)[_t]===t)return Tt;return-1}function Xo(e,t,n){var r=t[dt].data[e],o=r.directiveStart;return 0==o?Ke:(!n&&2&r.flags&&o++,t.slice(o,r.directiveEnd))}function ei(e){throw new Error("Multiple components match node with tagname "+e.tagName)}function ti(){throw new Error("Cannot mix multi providers and regular providers")}
/**
    * @license
    * Copyright Google Inc. All Rights Reserved.
    *
    * Use of this source code is governed by an MIT-style license that can be
    * found in the LICENSE file at https://angular.io/license
    */
function ni(e,t,n){for(var r=e.length;;){var o=e.indexOf(t,n);if(-1===o)return o;if(0===o||e.charCodeAt(o-1)<=32){var i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var ri="ng-template";function oi(e,t,n){for(var r=0;r<e.length;){var o=e[r++];if(n&&"class"===o){if(-1!==ni((o=e[r]).toLowerCase(),t,0))return!0}else if(1===o){for(;r<e.length&&"string"==typeof(o=e[r++]);)if(o.toLowerCase()===t)return!0;return!1}}return!1}function ii(e,t,n){return t===(0!==e.type||n?e.tagName:ri)}function ai(e,t,n){for(var r=4,o=e.attrs||[],i=function a(e){for(var t=0;t<e.length;t++)if(Bn(e[t]))return t;return e.length}(o),u=!1,s=0;s<t.length;s++){var l=t[s];if("number"!=typeof l){if(!u)if(4&r){if(r=2|1&r,""!==l&&!ii(e,l,n)||""===l&&1===t.length){if(ui(r))return!1;u=!0}}else{var c=8&r?l:t[++s];if(8&r&&null!==e.attrs){if(!oi(e.attrs,c,n)){if(ui(r))return!1;u=!0}continue}var f=si(8&r?"class":l,o,0==e.type&&e.tagName!==ri,n);if(-1===f){if(ui(r))return!1;u=!0;continue}if(""!==c){var d;d=f>i?"":o[f+1].toLowerCase();var p=8&r?d:null;if(p&&-1!==ni(p,c,0)||2&r&&c!==d){if(ui(r))return!1;u=!0}}}}else{if(!u&&!ui(r)&&!ui(l))return!1;if(u&&ui(l))continue;u=!1,r=l|1&r}}return ui(r)||u}function ui(e){return 0==(1&e)}function si(e,t,n,r){if(null===t)return-1;var o=0;if(r||!n){for(var i=!1;o<t.length;){var a=t[o];if(a===e)return o;if(3===a||6===a)i=!0;else{if(1===a||2===a){for(var u=t[++o];"string"==typeof u;)u=t[++o];continue}if(4===a)break;if(0===a){o+=4;continue}}o+=i?1:2}return-1}return function s(e,t){var n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){if(e[n]===t)return n;n++}return-1}(t,e)}function li(e,t,n){void 0===n&&(n=!1);for(var r=0;r<t.length;r++)if(ai(e,t[r],n))return!0;return!1}function ci(e,t){e:for(var n=0;n<t.length;n++){var r=t[n];if(e.length===r.length){for(var o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function fi(e,t){return e?":not("+t.trim()+")":t}function di(e){for(var t=e[0],n=1,r=2,o="",i=!1;n<e.length;){var a=e[n];if("string"==typeof a)if(2&r){var u=e[++n];o+="["+a+(u.length>0?'="'+u+'"':"")+"]"}else 8&r?o+="."+a:4&r&&(o+=" "+a);else""===o||ui(a)||(t+=fi(i,o),o=""),r=a,i=i||!ui(r);n++}return""!==o&&(t+=fi(i,o)),t}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
var pi={};
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function hi(e){var t=e[ht];return Mt(t)?t[ht]:t}function vi(e){return function t(e){for(var t=Ft(e)?e:Lo(e);t&&!(512&t[pt]);)t=hi(t);return t}(e)[_t]}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function yi(e){mi(Gt(),Kt(),_n()+e,tn())}function gi(e){mi(Gt(),Kt(),e,tn())}function mi(e,t,n,r){if(!r)if(3==(3&t[pt])){var o=e.preOrderCheckHooks;null!==o&&En(t,o,n)}else{var i=e.preOrderHooks;null!==i&&kn(t,i,0,n)}wn(n)}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var _i={marker:"element"},wi={marker:"comment"};
/**
    * @license
    * Copyright Google Inc. All Rights Reserved.
    *
    * Use of this source code is governed by an MIT-style license that can be
    * found in the LICENSE file at https://angular.io/license
    */
function bi(e,t){return e<<17|t<<2}function Ci(e){return e>>17&32767}function Ii(e){return 2==(2&e)}function xi(e){return 2|e}function Ei(e){return(131068&e)>>2}function ki(e,t){return-131069&e|t<<2}function Oi(e){return 1==(1&e)}function Pi(e){return 1|e}function Ni(e,t){for(var n=e.tView_.data,r=[],o=t?e.classBindings:e.styleBindings,i=Ci(o),a=Ei(o),u=0!==a,s=u?a:i;0!==s;){var l=n[s+1];r.unshift({key:n[s],index:s,isTemplate:u,prevDuplicate:Ii(l),nextDuplicate:Oi(l),nextIndex:Ei(l),prevIndex:Ci(l)}),s===i&&(u=!1),s=Ci(l)}return r.push((t?e.residualClasses:e.residualStyles)||null),r}function Ti(e,t){for(;e;)t.push(e.template_),e=e.next}function ji(e){if(e){var t=e.debug;return Ve(t,"Object does not have a debug representation."),t}return e}function Di(e,t){void 0===t&&(t=!1);var n=Do(e);if(n){var r=n.nodeType===Node.TEXT_NODE,o=(r?n.textContent:n.outerHTML)||"";return t||r?o:o.split(">"+n.innerHTML+"<")[0]+">"}return null}function Ai(e,t){if(e){for(var n=[],r=e;r;)n.push(Si(r,t,r.index)),r=r.next;return n}return null}function Si(e,t,n){var r=t[n],o=Do(r),i=ji(function a(e){for(;Array.isArray(e);){if(e.length>=Tt-1)return e;e=e[ft]}return null}(r));return{html:Di(o),native:o,nodes:Ai(e.child,t),component:i}}Object.defineProperty(function Ri(e,t,n,r,o,i,a,u,s,l,c,f,d,p,h,v,y,g,m,_,w,b,C,I,x,E,k,O,P,N){this.type=e,this.id=t,this.blueprint=n,this.template=r,this.queries=o,this.viewQuery=i,this.node=a,this.data=u,this.bindingStartIndex=s,this.expandoStartIndex=l,this.expandoInstructions=c,this.firstCreatePass=f,this.firstUpdatePass=d,this.staticViewQueries=p,this.staticContentQueries=h,this.preOrderHooks=v,this.preOrderCheckHooks=y,this.contentHooks=g,this.contentCheckHooks=m,this.viewHooks=_,this.viewCheckHooks=w,this.destroyHooks=b,this.cleanup=C,this.contentQueries=I,this.components=x,this.directiveRegistry=E,this.pipeRegistry=k,this.firstChild=O,this.schemas=P,this.consts=N}.prototype,"template_",{get:function(){var e=[];return Ti(this.firstChild,e),e.join("")},enumerable:!0,configurable:!0}),function(){function e(e,t,n,r,o,i,a,u,s,l,c,f,d,p,h,v,y,g,m,_,w,b,C,I,x,E,k,O,P){this.tView_=e,this.type=t,this.index=n,this.injectorIndex=r,this.directiveStart=o,this.directiveEnd=i,this.directiveStylingLast=a,this.propertyBindings=u,this.flags=s,this.providerIndexes=l,this.tagName=c,this.attrs=f,this.mergedAttrs=d,this.localNames=p,this.initialInputs=h,this.inputs=v,this.outputs=y,this.tViews=g,this.next=m,this.projectionNext=_,this.child=w,this.parent=b,this.projection=C,this.styles=I,this.residualStyles=x,this.classes=E,this.residualClasses=k,this.classBindings=O,this.styleBindings=P}Object.defineProperty(e.prototype,"type_",{get:function(){switch(this.type){case 0:return"TNodeType.Container";case 3:return"TNodeType.Element";case 4:return"TNodeType.ElementContainer";case 5:return"TNodeType.IcuContainer";case 1:return"TNodeType.Projection";case 2:return"TNodeType.View";default:return"TNodeType.???"}},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"flags_",{get:function(){var e=[];return 16&this.flags&&e.push("TNodeFlags.hasClassInput"),8&this.flags&&e.push("TNodeFlags.hasContentQuery"),32&this.flags&&e.push("TNodeFlags.hasStyleInput"),128&this.flags&&e.push("TNodeFlags.hasHostBindings"),2&this.flags&&e.push("TNodeFlags.isComponentHost"),1&this.flags&&e.push("TNodeFlags.isDirectiveHost"),64&this.flags&&e.push("TNodeFlags.isDetached"),4&this.flags&&e.push("TNodeFlags.isProjected"),e.join("|")},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"template_",{get:function(){var e=[];if(e.push("<",this.tagName||this.type_),this.attrs)for(var t=0;t<this.attrs.length;){var n=this.attrs[t++];if("number"==typeof n)break;var r=this.attrs[t++];e.push(" ",n,'="',r,'"')}return e.push(">"),Ti(this.child,e),e.push("</",this.tagName||this.type_,">"),e.join("")},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"styleBindings_",{get:function(){return Ni(this,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"classBindings_",{get:function(){return Ni(this,!0)},enumerable:!0,configurable:!0})}(),function(){function e(e){this._raw_lView=e}Object.defineProperty(e.prototype,"flags",{get:function(){var e=this._raw_lView[pt];return{__raw__flags__:e,initPhaseState:3&e,creationMode:!!(4&e),firstViewPass:!!(8&e),checkAlways:!!(16&e),dirty:!!(64&e),attached:!!(128&e),destroyed:!!(256&e),isRoot:!!(512&e),indexWithinInitPhase:e>>10}},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"parent",{get:function(){return ji(this._raw_lView[ht])},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"host",{get:function(){return Di(this._raw_lView[ft],!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"html",{get:function(){return(this.nodes||[]).map((function(e){return Di(e.native,!0)})).join("")},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"context",{get:function(){return this._raw_lView[_t]},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"nodes",{get:function(){var e=this._raw_lView;return Ai(e[dt].firstChild,e)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"tView",{get:function(){return this._raw_lView[dt]},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"cleanup",{get:function(){return this._raw_lView[mt]},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"injector",{get:function(){return this._raw_lView[wt]},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"rendererFactory",{get:function(){return this._raw_lView[bt]},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"renderer",{get:function(){return this._raw_lView[Ct]},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"sanitizer",{get:function(){return this._raw_lView[It]},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"childHead",{get:function(){return ji(this._raw_lView[xt])},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"next",{get:function(){return ji(this._raw_lView[vt])},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"childTail",{get:function(){return ji(this._raw_lView[Et])},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"declarationView",{get:function(){return ji(this._raw_lView[kt])},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"queries",{get:function(){return this._raw_lView[yt]},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"tHost",{get:function(){return this._raw_lView[gt]},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"childViews",{get:function(){for(var e=[],t=this.childHead;t;)e.push(t),t=t.next;return e},enumerable:!0,configurable:!0})}(),function(){function e(e){this._raw_lContainer=e}Object.defineProperty(e.prototype,"activeIndex",{get:function(){return qo(this._raw_lContainer)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"hasTransplantedViews",{get:function(){return 1==(1&this._raw_lContainer[Dt])},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"views",{get:function(){return this._raw_lContainer.slice(Vt).map(ji)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"parent",{get:function(){return ji(this._raw_lContainer[ht])},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"movedViews",{get:function(){return this._raw_lContainer[At]},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"host",{get:function(){return this._raw_lContainer[ft]},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"native",{get:function(){return this._raw_lContainer[St]},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"next",{get:function(){return ji(this._raw_lContainer[vt])},enumerable:!0,configurable:!0})}();var Vi=function(){function e(e,t,n,r){this.__raw_opCode=e,this._lView=t,this.nodeIndex=n,this.type=r}return Object.defineProperty(e.prototype,"tNode",{get:function(){return Vo(this._lView[dt],this.nodeIndex)},enumerable:!0,configurable:!0}),e}();Object.defineProperty(function Fi(e,t){this.__raw_opCodes=e,this.__lView=t}.prototype,"operations",{get:function(){for(var e=this.__lView,t=this.__raw_opCodes,n=[],r=0;r<t.length;r++){var o=t[r],i=void 0;if("string"==typeof o&&(i={__raw_opCode:o,type:"Create Text Node",nodeIndex:t[++r],text:o}),"number"==typeof o)switch(7&o){case 1:i=new Vi(o,e,o>>>17,"AppendChild");break;case 0:i=new Vi(o,e,o>>>3,"Select");break;case 5:var a=o>>>3;i=new Vi(o,e,a,"ElementEnd");break;case 4:(i=new Vi(o,e,a=o>>>3,"Attr")).attrName=t[++r],i.attrValue=t[++r]}if(!i)switch(o){case wi:i={__raw_opCode:o,type:"COMMENT_MARKER",commentValue:t[++r],nodeIndex:t[++r]};break;case _i:i={__raw_opCode:o,type:"ELEMENT_MARKER"}}i||(i={__raw_opCode:o,type:"Unknown Op Code",code:o}),n.push(i)}return n},enumerable:!0,configurable:!0}),Object.defineProperty(function Mi(e,t,n){this.__raw_opCodes=e,this.icus=t,this.__lView=n}.prototype,"operations",{get:function(){for(var e=this.__lView,t=this.__raw_opCodes,n=this.icus,r=[],o=0;o<t.length;o++){for(var i=t[o],a=t[++o],u="",s=o+1;s<=o+a;s++){var l=t[s];if("string"==typeof l)u+=l;else if("number"==typeof l)if(l<0)u+="�"+(-l-1)+"�";else{var c=l>>>2,f=void 0,d=void 0;switch(3&l){case 1:var p=t[++s],h=t[++s];r.push({__raw_opCode:l,checkBit:i,type:"Attr",attrValue:u,attrName:p,sanitizeFn:h});break;case 0:r.push({__raw_opCode:l,checkBit:i,type:"Text",nodeIndex:c,text:u});break;case 2:d=n[f=t[++s]];var v=new Vi(l,e,c,"IcuSwitch");v.tIcuIndex=f,v.checkBit=i,v.mainBinding=u,v.tIcu=d,r.push(v);break;case 3:d=n[f=t[++s]],(v=new Vi(l,e,c,"IcuUpdate")).tIcuIndex=f,v.checkBit=i,v.tIcu=d,r.push(v)}}}o+=a}return r},enumerable:!0,configurable:!0});var Hi=Promise.resolve(null);function Li(e,t){var n=e.contentQueries;if(null!==n)for(var r=0;r<n.length;r+=2){var o=n[r+1];if(-1!==o){var i=e.data[o];fn(n[r]),i.contentQueries(2,t[o],o)}}}function Bi(e,t,n){return Mn(t)?t.createElement(e,n):null===n?t.createElement(e):t.createElementNS(n,e)}function Ui(e,t,n,r,o,i,a,u,s,l){var c=t.blueprint.slice();return c[ft]=o,c[pt]=140|r,zo(c),c[ht]=c[kt]=e,c[_t]=n,c[bt]=a||e&&e[bt],c[Ct]=u||e&&e[Ct],c[It]=s||e&&e[It]||null,c[wt]=l||e&&e[wt]||null,c[gt]=i,c[Ot]=2==t.type?e[Ot]:c,c}function Qi(e,t,n,r,o,i){var a=n+Tt,u=e.data[a]||function s(e,t,n,r,o,i){var a=Yt(),u=Xt(),s=u?a:a&&a.parent,l=e.data[n]=ta(0,s&&s!==t?s:null,r,n,o,i);return null===e.firstChild&&(e.firstChild=l),a&&(!u||null!=a.child||null===l.parent&&2!==a.type?u||(a.next=l):a.child=l),l}(e,t,a,r,o,i);return $t(u,!0),u}function zi(e,t,n,r){var o=e.node;return null==o&&(e.node=o=ta(0,t,2,n,null,null)),r[gt]=o}function qi(e,t,n){hn(t,t[gt]);try{var r=e.viewQuery;null!==r&&ka(1,r,n);var o=e.template;null!==o&&Ki(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),e.staticContentQueries&&Li(e,t),e.staticViewQueries&&ka(2,e.viewQuery,n);var i=e.components;null!==i&&function a(e,t){for(var n=0;n<t.length;n++)wa(e,t[n])}(t,i)}finally{t[pt]&=-5,gn()}}function Zi(e,t,n,r){var o=t[pt];if(256!=(256&o)){hn(t,t[gt]);var i=tn();try{zo(t),an(e.bindingStartIndex),null!==n&&Ki(e,t,n,2,r);var a=3==(3&o);if(!i)if(a){var u=e.preOrderCheckHooks;null!==u&&En(t,u,null)}else{var s=e.preOrderHooks;null!==s&&kn(t,s,0,null),On(t,0)}if(function l(e){for(var t=e[xt];null!==t;){var n=void 0;if(Mt(t)&&(n=t[Dt])>>1==-1){for(var r=Vt;r<t.length;r++){var o=t[r],i=o[dt];Uo(o)&&Zi(i,o,i.template,o[_t])}0!=(1&n)&&ma(t,e[Ot])}t=t[vt]}}(t),null!==e.contentQueries&&Li(e,t),!i)if(a){var c=e.contentCheckHooks;null!==c&&En(t,c)}else{var f=e.contentHooks;null!==f&&kn(t,f,1),On(t,1)}!function d(e,t){try{var n=e.expandoInstructions;if(null!==n)for(var r=e.expandoStartIndex,o=-1,i=0;i<n.length;i++){var a=n[i];if("number"==typeof a)if(a<=0){wn(0-a);var u=n[++i];o=r+=An+u}else r+=a;else null!==a&&(ln(r,o),a(2,t[o])),o++}}finally{wn(-1)}}(e,t);var p=e.components;null!==p&&function h(e,t){for(var n=0;n<t.length;n++)_a(e,t[n])}(t,p);var v=e.viewQuery;if(null!==v&&ka(2,v,r),!i)if(a){var y=e.viewCheckHooks;null!==y&&En(t,y)}else{var g=e.viewHooks;null!==g&&kn(t,g,2),On(t,2)}!0===e.firstUpdatePass&&(e.firstUpdatePass=!1),i||(t[pt]&=-73)}finally{gn()}}}function Wi(e,t,n,r){var o=t[bt],i=!tn(),a=Bo(t);try{i&&!a&&o.begin&&o.begin(),a&&qi(e,t,r),Zi(e,t,n,r)}finally{i&&!a&&o.end&&o.end()}}function Ki(e,t,n,r,o){var i=_n();try{wn(-1),2&r&&t.length>Tt&&mi(e,t,0,tn()),n(r,o)}finally{wn(i)}}function Gi(e,t,n){if(Ht(t))for(var r=t.directiveEnd,o=t.directiveStart;o<r;o++){var i=e.data[o];i.contentQueries&&i.contentQueries(1,n[o],o)}}function Ji(e,t,n){qt()&&(function r(e,t,n,o){var i=n.directiveStart,a=n.directiveEnd;e.firstCreatePass||ar(n,t),Go(o,t);for(var u=n.initialInputs,s=i;s<a;s++){var l=e.data[s],c=Ut(l);c&&pa(t,n,l);var f=yr(t,e,s,n);Go(f,t),null!==u&&va(0,s-i,f,l,0,u),c&&(Mo(n.index,t)[_t]=f)}}(e,t,n,So(n,t)),128==(128&n.flags)&&function o(e,t,n){var r=n.directiveStart,o=n.directiveEnd,i=e.expandoInstructions,a=e.firstCreatePass,u=n.index-Tt;try{wn(u);for(var s=r;s<o;s++){var l=e.data[s];null!==l.hostBindings||0!==l.hostVars||null!==l.hostAttrs?ua(l,t[s]):a&&i.push(null)}}finally{wn(-1)}}(e,t,n))}function Yi(e,t,n){void 0===n&&(n=So);var r=t.localNames;if(null!==r)for(var o=t.index+1,i=0;i<r.length;i+=2){var a=r[i+1],u=-1===a?n(t,e):e[a];e[o++]=u}}function $i(e){return e.tView||(e.tView=Xi(1,-1,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts))}function Xi(e,t,n,r,o,i,a,u,s,l){var c=Tt+r,f=c+o,d=function p(e,t){for(var n=[],r=0;r<t;r++)n.push(r<e?null:pi);return n}(c,f);return d[dt]={type:e,id:t,blueprint:d,template:n,queries:null,viewQuery:u,node:null,data:d.slice().fill(null,c),bindingStartIndex:c,expandoStartIndex:f,expandoInstructions:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:"function"==typeof i?i():i,pipeRegistry:"function"==typeof a?a():a,firstChild:null,schemas:s,consts:l}}function ea(t,n,r){if(Mn(t))return t.selectRootElement(n,r===e.ViewEncapsulation.ShadowDom);var o="string"==typeof n?t.querySelector(n):n;return o.textContent="",o}function ta(e,t,n,r,o,i){return{type:n,index:r,injectorIndex:t?t.injectorIndex:-1,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,propertyBindings:null,flags:0,providerIndexes:0,tagName:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tViews:null,next:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,residualStyles:void 0,classes:null,residualClasses:void 0,classBindings:0,styleBindings:0}}function na(e,t,n){for(var r in e)if(e.hasOwnProperty(r)){var o=e[r];(n=null===n?{}:n).hasOwnProperty(r)?n[r].push(t,o):n[r]=[t,o]}return n}function ra(e,t,n,r,o,i,a,u){var s,l=Ao(n,t),c=Vo(e,n),f=c.inputs;if(!a&&null!=f&&(s=f[r]))Da(e,t,s,r,o),Lt(c)&&function d(e,t){var n=Mo(t,e);16&n[pt]||(n[pt]|=64)}(t,n+Tt);else if(3===c.type){r=function p(e){return"class"===e?"className":"for"===e?"htmlFor":"formaction"===e?"formAction":"innerHtml"===e?"innerHTML":"readonly"===e?"readOnly":"tabindex"===e?"tabIndex":e}(r);var h=u?u(c,t):t[Ct];o=null!=i?i(o,c.tagName||"",r):o,Mn(h)?h.setProperty(l,r,o):Un(r)||(l.setProperty?l.setProperty(r,o):l[r]=o)}}function oa(e,t,n,r){var o=!1;if(qt()){var i=function a(e,t,n){var r=e.directiveRegistry,o=null;if(r)for(var i=0;i<r.length;i++){var a=r[i];li(n,a.selectors,!1)&&(o||(o=[]),cr(ar(n,t),e,a.type),Ut(a)?(2&n.flags&&ei(n),la(e,n),o.unshift(a)):o.push(a))}return o}(e,t,n),u=null===r?null:{"":-1};if(null!==i){var s=0;o=!0,fa(n,e.data.length,i.length);for(var l=0;l<i.length;l++)(d=i[l]).providersResolver&&d.providersResolver(d);sa(e,n,i.length);var c=!1,f=!1;for(l=0;l<i.length;l++){var d;n.mergedAttrs=Qn(n.mergedAttrs,(d=i[l]).hostAttrs),da(e,t,d),ca(e.data.length-1,d,u),null!==d.contentQueries&&(n.flags|=8),null===d.hostBindings&&null===d.hostAttrs&&0===d.hostVars||(n.flags|=128),!c&&(d.onChanges||d.onInit||d.doCheck)&&((e.preOrderHooks||(e.preOrderHooks=[])).push(n.index-Tt),c=!0),f||!d.onChanges&&!d.doCheck||((e.preOrderCheckHooks||(e.preOrderCheckHooks=[])).push(n.index-Tt),f=!0),ia(e,d),s+=d.hostVars}!function p(e,t){for(var n=t.directiveEnd,r=e.data,o=t.attrs,i=[],a=null,u=null,s=t.directiveStart;s<n;s++){var l=r[s],c=l.inputs;i.push(null!==o?ya(c,o):null),a=na(c,s,a),u=na(l.outputs,s,u)}null!==a&&((a.hasOwnProperty("class")||a.hasOwnProperty("className"))&&(t.flags|=16),a.hasOwnProperty("style")&&(t.flags|=32)),t.initialInputs=i,t.inputs=a,t.outputs=u}(e,n),aa(e,t,s)}u&&function h(e,t,n){if(t)for(var r=e.localNames=[],o=0;o<t.length;o+=2){var i=n[t[o+1]];if(null==i)throw new Error("Export of name '"+t[o+1]+"' not found!");r.push(t[o],i)}}(n,r,u)}return n.mergedAttrs=Qn(n.mergedAttrs,n.attrs),o}function ia(e,t){var n=e.expandoInstructions;n.push(t.hostBindings),0!==t.hostVars&&n.push(t.hostVars)}function aa(e,t,n){for(var r=0;r<n;r++)t.push(pi),e.blueprint.push(pi),e.data.push(null)}function ua(e,t){null!==e.hostBindings&&e.hostBindings(1,t)}function sa(e,t,n){var r=Tt-t.index,o=e.data.length-(65535&t.providerIndexes);(e.expandoInstructions||(e.expandoInstructions=[])).push(r,o,n)}function la(e,t){t.flags|=2,(e.components||(e.components=[])).push(t.index)}function ca(e,t,n){if(n){if(t.exportAs)for(var r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;Ut(t)&&(n[""]=e)}}function fa(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function da(e,t,n){e.data.push(n);var r=n.factory||(n.factory=lt(n.type)),o=new Rn(r,Ut(n),null);e.blueprint.push(o),t.push(o)}function pa(e,t,n){var r=So(t,e),o=$i(n),i=e[bt],a=ba(e,Ui(e,o,null,n.onPush?64:16,r,t,i,i.createRenderer(r,n)));e[t.index]=a}function ha(e,t,n,r,o,i,a){var u=Ao(e,o),s=o[Ct];if(null==n)Mn(s)?s.removeAttribute(u,t,a):u.removeAttribute(t);else{var l=Vo(r,e),c=null==i?Gn(n):i(n,l.tagName||"",t);Mn(s)?s.setAttribute(u,t,c,a):a?u.setAttributeNS(a,t,c):u.setAttribute(t,c)}}function va(e,t,n,r,o,i){var a=i[t];if(null!==a)for(var u=r.setInput,s=0;s<a.length;){var l=a[s++],c=a[s++],f=a[s++];null!==u?r.setInput(n,f,l,c):n[c]=f}}function ya(e,t){for(var n=null,r=0;r<t.length;){var o=t[r];if(0!==o)if(5!==o){if("number"==typeof o)break;e.hasOwnProperty(o)&&(null===n&&(n=[]),n.push(o,e[o],t[r+1])),r+=2}else r+=2;else r+=4}return n}function ga(e,t,n,r){return new Array(e,!0,-2,t,null,null,r,n,null)}function ma(e,t){for(var n=e[At],r=0;r<n.length;r++){var o=n[r],i=o[ht][ht][Ot];if(i!==t&&0==(16&i[pt])){var a=o[dt];Zi(a,o,a.template,o[_t])}}}function _a(e,t){var n=Mo(t,e);if(Uo(n)&&80&n[pt]){var r=n[dt];Zi(r,n,r.template,n[_t])}}function wa(e,t){var n=Mo(t,e),r=n[dt];!function o(e,t){for(var n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}(r,n),qi(r,n,n[_t])}function ba(e,t){return e[xt]?e[Et][vt]=t:e[xt]=t,e[Et]=t,t}function Ca(e){for(;e;){e[pt]|=64;var t=hi(e);if(Qt(e)&&!t)return e;e=t}return null}function Ia(e){for(var t=0;t<e.components.length;t++){var n=e.components[t],r=Lo(n),o=r[dt];Wi(o,r,o.template,n)}}function xa(e,t,n){var r=t[bt];r.begin&&r.begin();try{Zi(e,t,e.template,n)}catch(e){throw ja(t,e),e}finally{r.end&&r.end()}}function Ea(e){Ia(e[_t])}function ka(e,t,n){fn(0),t(e,n)}var Oa=Hi;function Pa(e){return e[mt]||(e[mt]=[])}function Na(e){return e.cleanup||(e.cleanup=[])}function Ta(e,t){return t[e.index][Ct]}function ja(e,t){var n=e[wt],r=n?n.get(Pr,null):null;r&&r.handleError(t)}function Da(e,t,n,r,o){for(var i=0;i<n.length;){var a=n[i++],u=n[i++],s=t[a],l=e.data[a];null!==l.setInput?l.setInput(s,o,r,u):s[u]=o}}function Aa(e,t,n){var r=Ao(t,e),o=e[Ct];Mn(o)?o.setValue(r,n):r.textContent=n}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function Sa(e,t){var n=t[ht];return-1===e.index?Mt(n)?n:null:n}function Ra(e,t){var n=Sa(e,t);return n?Ja(t[Ct],n[St]):null}function Va(e,t,n,r,o){if(null!=r){var i=void 0,a=!1;Mt(r)?i=r:Ft(r)&&(a=!0,r=r[ft]);var u=Do(r);0===e&&null!==n?null==o?Ka(t,n,u):Wa(t,n,u,o||null):1===e&&null!==n?Wa(t,n,u,o||null):2===e?eu(t,u,a):3===e&&t.destroyNode(u),null!=i&&function s(e,t,n,r,o){var i=n[St];i!==Do(n)&&Va(t,e,r,i,o);for(var a=Vt;a<n.length;a++){var u=n[a];nu(u[dt],u,e,t,r,i)}}(t,e,i,n,o)}}function Fa(e,t){return Mn(t)?t.createText(e):t.createTextNode(e)}function Ma(e,t,n,r){var o=Ra(e.node,t);o&&nu(e,t,t[Ct],n?1:2,o,r)}function Ha(e,t,n,r){var o=Vt+r,i=n.length;r>0&&(n[o-1][vt]=t),r<i-Vt?(t[vt]=n[o],Le(n,Vt+r,t)):(n.push(t),t[vt]=null),t[ht]=n;var a=t[Pt];null!==a&&n!==a&&function u(e,t){var n=e[At],r=t[ht][ht][Ot];16!=(16&r[pt])&&t[Ot]!==r&&(e[Dt]|=1),null===n?e[At]=[t]:n.push(t)}(a,t);var s=t[yt];null!==s&&s.insertView(e),t[pt]|=128}function La(e,t){var n=e[At],r=n.indexOf(t);n.splice(r,1)}function Ba(e,t){if(!(e.length<=Vt)){var n=Vt+t,r=e[n];if(r){var o=r[Pt];null!==o&&o!==e&&La(o,r),t>0&&(e[n-1][vt]=r[vt]);var i=Be(e,Vt+t);Ma(r[dt],r,!1,null);var a=i[yt];null!==a&&a.detachView(i[dt]),r[ht]=null,r[vt]=null,r[pt]&=-129}return r}}function Ua(e,t){var n=Ba(e,t);n&&Qa(n[dt],n)}function Qa(e,t){if(!(256&t[pt])){var n=t[Ct];Mn(n)&&n.destroyNode&&nu(e,t,n,3,null,null),function r(e){var t=e[xt];if(!t)return qa(e[dt],e);for(;t;){var n=null;if(Ft(t))n=t[xt];else{var r=t[Vt];r&&(n=r)}if(!n){for(;t&&!t[vt]&&t!==e;)Ft(t)&&qa(t[dt],t),t=za(t,e);null===t&&(t=e),Ft(t)&&qa(t[dt],t),n=t&&t[vt]}t=n}}(t)}}function za(e,t){var n;return Ft(e)&&(n=e[gt])&&2===n.type?Sa(n,e):e[ht]===t?null:e[ht]}function qa(e,t){if(!(256&t[pt])){t[pt]&=-129,t[pt]|=256,function n(e,t){var n;if(null!=e&&null!=(n=e.destroyHooks))for(var r=0;r<n.length;r+=2){var o=t[n[r]];o instanceof Rn||n[r+1].call(o)}}(e,t),function r(e,t){var n=e.cleanup;if(null!==n){for(var r=t[mt],o=0;o<n.length-1;o+=2)if("string"==typeof n[o]){var i=n[o+1],a="function"==typeof i?i(t):Do(t[i]),u=n[o+3];"boolean"==typeof u?a.removeEventListener(n[o],r[n[o+2]],u):u>=0?r[u]():r[-u].unsubscribe(),o+=2}else n[o].call(r[n[o+1]]);t[mt]=null}}(e,t);var o=t[gt];o&&3===o.type&&Mn(t[Ct])&&t[Ct].destroy();var i=t[Pt];if(null!==i&&Mt(t[ht])){i!==t[ht]&&La(i,t);var a=t[yt];null!==a&&a.detachView(e)}}}function Za(t,n,r){for(var o=n.parent;null!=o&&(4===o.type||5===o.type);)o=(n=o).parent;if(null==o){var i=r[gt];return 2===i.type?Ra(i,r):r[ft]}if(n&&5===n.type&&4&n.flags)return So(n,r).parentNode;if(2&o.flags){var a=t.data,u=a[a[o.index].directiveStart].encapsulation;if(u!==e.ViewEncapsulation.ShadowDom&&u!==e.ViewEncapsulation.Native)return null}return So(o,r)}function Wa(e,t,n,r){Mn(e)?e.insertBefore(t,n,r):t.insertBefore(n,r,!0)}function Ka(e,t,n){Mn(e)?e.appendChild(t,n):t.appendChild(n)}function Ga(e,t,n,r){null!==r?Wa(e,t,n,r):Ka(e,t,n)}function Ja(e,t){return Mn(e)?e.parentNode(t):t.parentNode}function Ya(e,t){if(2===e.type){var n=Sa(e,t);return null===n?null:Xa(n.indexOf(t,Vt)-Vt,n)}return 4===e.type||5===e.type?So(e,t):null}function $a(e,t,n,r){var o=Za(e,r,t);if(null!=o){var i=t[Ct],a=Ya(r.parent||t[gt],t);if(Array.isArray(n))for(var u=0;u<n.length;u++)Ga(i,o,n[u],a);else Ga(i,o,n,a)}}function Xa(e,t){var n=Vt+e+1;if(n<t.length){var r=t[n],o=r[dt].firstChild;if(null!==o)return function e(t,n){if(null!==n){var r=n.type;if(3===r)return So(n,t);if(0===r)return Xa(-1,t[n.index]);if(4===r||5===r){var o=n.child;if(null!==o)return e(t,o);var i=t[n.index];return Mt(i)?Xa(-1,i):Do(i)}var a=t[Ot],u=a[gt],s=hi(a),l=u.projection[n.projection];return null!=l?e(s,l):e(t,n.next)}return null}(r,o)}return t[St]}function eu(e,t,n){var r=Ja(e,t);r&&function o(e,t,n,r){Mn(e)?e.removeChild(t,n,r):t.removeChild(n)}(e,r,t,n)}function tu(e,t,n,r,o,i,a){for(;null!=n;){var u=r[n.index],s=n.type;a&&0===t&&(u&&Go(Do(u),r),n.flags|=4),64!=(64&n.flags)&&(4===s||5===s?(tu(e,t,n.child,r,o,i,!1),Va(t,e,o,u,i)):1===s?ou(e,t,r,n,o,i):Va(t,e,o,u,i)),n=a?n.projectionNext:n.next}}function nu(e,t,n,r,o,i){tu(n,r,e.node.child,t,o,i,!1)}function ru(e,t,n){ou(t[Ct],0,t,n,Za(e,n,t),Ya(n.parent||t[gt],t))}function ou(e,t,n,r,o,i){var a=n[Ot],u=a[gt].projection[r.projection];if(Array.isArray(u))for(var s=0;s<u.length;s++)Va(t,e,o,u[s],i);else tu(e,t,u,a[ht],o,i,!0)}function iu(e,t,n){Mn(e)?e.setAttribute(t,"style",n):t.style.cssText=n}function au(e,t,n){Mn(e)?""===n?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n):t.className=n}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
var uu,su,lu,cu=function(){function e(e,t){this._lView=e,this._cdRefInjectingView=t,this._appRef=null,this._viewContainerRef=null,this._tViewNode=null}return Object.defineProperty(e.prototype,"rootNodes",{get:function(){var e=this._lView;return null==e[ft]?function e(t,n,r,o,i){for(void 0===i&&(i=!1);null!==r;){var a=n[r.index];if(null!==a&&o.push(Do(a)),Mt(a))for(var u=Vt;u<a.length;u++){var s=a[u],l=s[dt].firstChild;null!==l&&e(s[dt],s,l,o)}var c=r.type;if(4===c||5===c)e(t,n,r.child,o);else if(1===c){var f=n[Ot],d=f[gt],p=hi(f),h=d.projection[r.projection];null!==h&&null!==p&&e(p[dt],p,h,o,!0)}r=i?r.projectionNext:r.next}return o}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */(e[dt],e,e[gt].child,[]):[]},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"context",{get:function(){return this._lView[_t]},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"destroyed",{get:function(){return 256==(256&this._lView[pt])},enumerable:!0,configurable:!0}),e.prototype.destroy=function(){if(this._appRef)this._appRef.detachView(this);else if(this._viewContainerRef){var e=this._viewContainerRef.indexOf(this);e>-1&&this._viewContainerRef.detach(e),this._viewContainerRef=null}Qa(this._lView[dt],this._lView)},e.prototype.onDestroy=function(e){!function t(e,n,r){Pa(n).push(r),e.firstCreatePass&&Na(e).push(n[mt].length-1,null)}(this._lView[dt],this._lView,e)},e.prototype.markForCheck=function(){Ca(this._cdRefInjectingView||this._lView)},e.prototype.detach=function(){this._lView[pt]&=-129},e.prototype.reattach=function(){this._lView[pt]|=128},e.prototype.detectChanges=function(){xa(this._lView[dt],this._lView,this.context)},e.prototype.checkNoChanges=function(){!function e(t,n,r){nn(!0);try{xa(t,n,r)}finally{nn(!1)}}(this._lView[dt],this._lView,this.context)},e.prototype.attachToViewContainerRef=function(e){if(this._appRef)throw new Error("This view is already attached directly to the ApplicationRef!");this._viewContainerRef=e},e.prototype.detachFromAppRef=function(){this._appRef=null,function e(t,n){nu(t,n,n[Ct],2,null,null)}(this._lView[dt],this._lView)},e.prototype.attachToAppRef=function(e){if(this._viewContainerRef)throw new Error("This view is already attached to a ViewContainer!");this._appRef=e},e}(),fu=function(e){function t(t){var n=e.call(this,t)||this;return n._view=t,n}return o(t,e),t.prototype.detectChanges=function(){Ea(this._view)},t.prototype.checkNoChanges=function(){!function e(t){nn(!0);try{Ea(t)}finally{nn(!1)}}(this._view)},Object.defineProperty(t.prototype,"context",{get:function(){return null},enumerable:!0,configurable:!0}),t}(cu);function du(e,t,n){return uu||(uu=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t}(e)),new uu(So(t,n))}function pu(e,t,n,r){return su||(su=function(e){function t(t,n,r){var o=e.call(this)||this;return o._declarationView=t,o._declarationTContainer=n,o.elementRef=r,o}return o(t,e),t.prototype.createEmbeddedView=function(e){var t=this._declarationTContainer.tViews,n=Ui(this._declarationView,t,e,16,null,t.node);n[Pt]=this._declarationView[this._declarationTContainer.index];var r=this._declarationView[yt];null!==r&&(n[yt]=r.createEmbeddedView(t)),qi(t,n,e);var o=new cu(n);return o._tViewNode=n[gt],o},t}(e)),0===n.type?new su(r,n,du(t,n,r)):null}function hu(e,t,n,r){var i;lu||(lu=function(e){function n(t,n,r){var o=e.call(this)||this;return o._lContainer=t,o._hostTNode=n,o._hostView=r,o}return o(n,e),Object.defineProperty(n.prototype,"element",{get:function(){return du(t,this._hostTNode,this._hostView)},enumerable:!0,configurable:!0}),Object.defineProperty(n.prototype,"injector",{get:function(){return new _r(this._hostTNode,this._hostView)},enumerable:!0,configurable:!0}),Object.defineProperty(n.prototype,"parentInjector",{get:function(){var e=lr(this._hostTNode,this._hostView),t=Kn(e,this._hostView),n=function r(e,t,n){if(n.parent&&-1!==n.parent.injectorIndex){for(var r=n.parent.injectorIndex,o=n.parent;null!=o.parent&&r==o.parent.injectorIndex;)o=o.parent;return o}for(var i=Wn(e),a=t,u=t[gt];i>1;)u=(a=a[kt])[gt],i--;return u}(e,this._hostView,this._hostTNode);return qn(e)&&null!=n?new _r(n,t):new _r(null,this._hostView)},enumerable:!0,configurable:!0}),n.prototype.clear=function(){for(;this.length>0;)this.remove(this.length-1)},n.prototype.get=function(e){return null!==this._lContainer[Rt]&&this._lContainer[Rt][e]||null},Object.defineProperty(n.prototype,"length",{get:function(){return this._lContainer.length-Vt},enumerable:!0,configurable:!0}),n.prototype.createEmbeddedView=function(e,t,n){var r=e.createEmbeddedView(t||{});return this.insert(r,n),r},n.prototype.createComponent=function(e,t,n,r,o){var i=n||this.parentInjector;if(!o&&null==e.ngModule&&i){var a=i.get(Se,null);a&&(o=a)}var u=e.create(i,r,void 0,o);return this.insert(u.hostView,t),u},n.prototype.insert=function(e,t){var n=e._lView,r=n[dt];if(e.destroyed)throw new Error("Cannot insert a destroyed View in a ViewContainer!");if(this.allocateContainerIfNeeded(),function o(e){return Mt(e[ht])}(n)){var i=this.indexOf(e);if(-1!==i)this.detach(i);else{var a=n[ht],u=new lu(a,a[gt],a[ht]);u.detach(u.indexOf(e))}}var s=this._adjustIndex(t);return Ha(r,n,this._lContainer,s),Ma(r,n,!0,Xa(s,this._lContainer)),e.attachToViewContainerRef(this),Le(this._lContainer[Rt],s,e),e},n.prototype.move=function(e,t){if(e.destroyed)throw new Error("Cannot move a destroyed View in a ViewContainer!");return this.insert(e,t)},n.prototype.indexOf=function(e){var t=this._lContainer[Rt];return null!==t?t.indexOf(e):-1},n.prototype.remove=function(e){this.allocateContainerIfNeeded();var t=this._adjustIndex(e,-1);Ua(this._lContainer,t),Be(this._lContainer[Rt],t)},n.prototype.detach=function(e){this.allocateContainerIfNeeded();var t=this._adjustIndex(e,-1),n=Ba(this._lContainer,t);return n&&null!=Be(this._lContainer[Rt],t)?new cu(n):null},n.prototype._adjustIndex=function(e,t){return void 0===t&&(t=0),null==e?this.length+t:e},n.prototype.allocateContainerIfNeeded=function(){null===this._lContainer[Rt]&&(this._lContainer[Rt]=[])},n}(e));var a=r[n.index];if(Mt(a))!function u(e,t){e[Dt]=t<<1}(i=a,-1);else{var s=void 0;if(4===n.type)s=Do(a);else if(s=r[Ct].createComment(""),Qt(r)){var l=r[Ct],c=So(n,r);Wa(l,Ja(l,c),s,function f(e,t){return Mn(e)?e.nextSibling(t):t.nextSibling}(l,c))}else $a(r[dt],r,s,n);r[n.index]=i=ga(a,r,s,n),ba(r,i)}return new lu(i,n,r)}function vu(e){return void 0===e&&(e=!1),function t(e,n,r){if(!r&&Lt(e)){var o=Mo(e.index,n);return new cu(o,o)}return 3===e.type||0===e.type||4===e.type||5===e.type?new cu(n[Ot],n):null}(Yt(),Kt(),e)}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
var yu=function(){function e(){}return e.__NG_ELEMENT_ID__=function(){return mu()},e}(),gu=vu,mu=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t]},_u=Function;function wu(e){return"function"==typeof e}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var bu=/^function\s+\S+\(\)\s*{[\s\S]+\.apply\(this,\s*arguments\)/,Cu=/^class\s+[A-Za-z\d$_]*\s*extends\s+[^{]+{/,Iu=/^class\s+[A-Za-z\d$_]*\s*extends\s+[^{]+{[\s\S]*constructor\s*\(/,xu=/^class\s+[A-Za-z\d$_]*\s*extends\s+[^{]+{[\s\S]*constructor\s*\(\)\s*{\s+super\(\.\.\.arguments\)/,Eu=function(){function e(e){this._reflect=e||ee.Reflect}return e.prototype.isReflectionEnabled=function(){return!0},e.prototype.factory=function(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new(e.bind.apply(e,f([void 0],t)))}},e.prototype._zipTypesAndAnnotations=function(e,t){var n;n=Ue(void 0===e?t.length:e.length);for(var r=0;r<n.length;r++)n[r]=void 0===e?[]:e[r]&&e[r]!=Object?[e[r]]:[],t&&null!=t[r]&&(n[r]=n[r].concat(t[r]));return n},e.prototype._ownParameters=function(e,t){if(function n(e){return bu.test(e)||xu.test(e)||Cu.test(e)&&!Iu.test(e)}(e.toString()))return null;if(e.parameters&&e.parameters!==t.parameters)return e.parameters;var r=e.ctorParameters;if(r&&r!==t.ctorParameters){var o="function"==typeof r?r():r,i=o.map((function(e){return e&&e.type})),a=o.map((function(e){return e&&ku(e.decorators)}));return this._zipTypesAndAnnotations(i,a)}var u=e.hasOwnProperty(p)&&e[p],s=this._reflect&&this._reflect.getOwnMetadata&&this._reflect.getOwnMetadata("design:paramtypes",e);return s||u?this._zipTypesAndAnnotations(s,u):Ue(e.length)},e.prototype.parameters=function(e){if(!wu(e))return[];var t=Ou(e),n=this._ownParameters(e,t);return n||t===Object||(n=this.parameters(t)),n||[]},e.prototype._ownAnnotations=function(e,t){if(e.annotations&&e.annotations!==t.annotations){var n=e.annotations;return"function"==typeof n&&n.annotations&&(n=n.annotations),n}return e.decorators&&e.decorators!==t.decorators?ku(e.decorators):e.hasOwnProperty(d)?e[d]:null},e.prototype.annotations=function(e){if(!wu(e))return[];var t=Ou(e),n=this._ownAnnotations(e,t)||[];return(t!==Object?this.annotations(t):[]).concat(n)},e.prototype._ownPropMetadata=function(e,t){if(e.propMetadata&&e.propMetadata!==t.propMetadata){var n=e.propMetadata;return"function"==typeof n&&n.propMetadata&&(n=n.propMetadata),n}if(e.propDecorators&&e.propDecorators!==t.propDecorators){var r=e.propDecorators,o={};return Object.keys(r).forEach((function(e){o[e]=ku(r[e])})),o}return e.hasOwnProperty(h)?e[h]:null},e.prototype.propMetadata=function(e){if(!wu(e))return{};var t=Ou(e),n={};if(t!==Object){var r=this.propMetadata(t);Object.keys(r).forEach((function(e){n[e]=r[e]}))}var o=this._ownPropMetadata(e,t);return o&&Object.keys(o).forEach((function(e){var t=[];n.hasOwnProperty(e)&&t.push.apply(t,f(n[e])),t.push.apply(t,f(o[e])),n[e]=t})),n},e.prototype.ownPropMetadata=function(e){return wu(e)&&this._ownPropMetadata(e,Ou(e))||{}},e.prototype.hasLifecycleHook=function(e,t){return e instanceof _u&&t in e.prototype},e.prototype.guards=function(e){return{}},e.prototype.getter=function(e){return new Function("o","return o."+e+";")},e.prototype.setter=function(e){return new Function("o","v","return o."+e+" = v;")},e.prototype.method=function(e){return new Function("o","args","if (!o."+e+") throw new Error('\""+e+"\" is undefined');\n        return o."+e+".apply(o, args);")},e.prototype.importUri=function(e){return"object"==typeof e&&e.filePath?e.filePath:"./"+B(e)},e.prototype.resourceUri=function(e){return"./"+B(e)},e.prototype.resolveIdentifier=function(e,t,n,r){return r},e.prototype.resolveEnum=function(e,t){return e[t]},e}();function ku(e){return e?e.map((function(e){var t=e.type.annotationCls;return new(t.bind.apply(t,f([void 0],e.args?e.args:[])))})):[]}function Ou(e){var t=e.prototype?Object.getPrototypeOf(e.prototype):null;return(t?t.constructor:null)||Object}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var Pu=null;function Nu(){return Pu=Pu||new Eu}function Tu(e){return ju(Nu().parameters(e))}function ju(e){var t=te();return e.map((function(e){return function n(e,t){var n={token:null,host:!1,optional:!1,resolved:e.R3ResolvedDependencyType.Token,self:!1,skipSelf:!1};function r(t){n.resolved=e.R3ResolvedDependencyType.Token,n.token=t}if(Array.isArray(t)&&t.length>0)for(var o=0;o<t.length;o++){var i=t[o];if(void 0!==i){var a=Object.getPrototypeOf(i);if(i instanceof C||"Optional"===a.ngMetadataName)n.optional=!0;else if(i instanceof x||"SkipSelf"===a.ngMetadataName)n.skipSelf=!0;else if(i instanceof I||"Self"===a.ngMetadataName)n.self=!0;else if(i instanceof E||"Host"===a.ngMetadataName)n.host=!0;else if(i instanceof b)n.token=i.token;else if(i instanceof O){if(void 0===i.attributeName)throw new Error("Attribute name must be defined.");n.token=i.attributeName,n.resolved=e.R3ResolvedDependencyType.Attribute}else i===yu?(n.token=i,n.resolved=e.R3ResolvedDependencyType.ChangeDetectorRef):r(i)}}else void 0===t||Array.isArray(t)&&0===t.length?(n.token=void 0,n.resolved=W.Invalid):r(t);return n}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */(t,e)}))}var Du=P({provide:String,useValue:P});function Au(e){return void 0!==e.useClass}function Su(e){return void 0!==e.useFactory}function Ru(e,t){var n=t||{providedIn:null},r={name:e.name,type:e,typeArgumentCount:0,providedIn:n.providedIn,userDeps:void 0};return(Au(n)||Su(n))&&void 0!==n.deps&&(r.userDeps=ju(n.deps)),Au(n)?r.useClass=q(n.useClass):function o(e){return Du in e}(n)?r.useValue=q(n.useValue):Su(n)?r.useFactory=n.useFactory:function i(e){return void 0!==e.useExisting}(n)&&(r.useExisting=q(n.useExisting)),r}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var Vu=P({provide:String,useValue:P}),Fu=[];function Mu(e,t){if(!t){var n=(s=new Eu).parameters(e);return function(){return new(e.bind.apply(e,f([void 0],Oe(n))))}}if(Vu in t){var r=t;return function(){return r.useValue}}if(t.useExisting){var o=t;return function(){return Ie(q(o.useExisting))}}if(t.useFactory){var i=t;return function(){return i.useFactory.apply(i,f(Oe(i.deps||Fu)))}}if(t.useClass){var a=t,u=t.deps;if(!u){var s=new Eu;u=s.parameters(e)}return function(){var e;return new((e=q(a.useClass)).bind.apply(e,f([void 0],Oe(u))))}}var l=t.deps;return l||(s=new Eu,l=s.parameters(e)),function(){return new(e.bind.apply(e,f([void 0],Oe(l))))}}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var Hu=v("Injectable",void 0,void 0,void 0,(function(e,t){return Lu(e,t)})),Lu=function Bu(e,t){t&&void 0!==t.providedIn&&!A(e)&&(e.ɵprov=T({token:e,providedIn:t.providedIn,factory:Mu(e,t)}))},Uu=new ce("Set Injector scope."),Qu={},zu={},qu=[],Zu=void 0;function Wu(){return void 0===Zu&&(Zu=new Pe),Zu}function Ku(e,t,n,r){return void 0===t&&(t=null),void 0===n&&(n=null),t=t||Wu(),new Gu(e,n,t,r)}var Gu=function(){function t(e,t,n,r){var o=this;void 0===r&&(r=null),this.parent=n,this.records=new Map,this.injectorDefTypes=new Set,this.onDestroy=new Set,this._destroyed=!1;var i=[];t&&He(t,(function(n){return o.processProvider(n,e,t)})),He([e],(function(e){return o.processInjectorType(e,[],i)})),this.records.set(fe,$u(void 0,this));var a=this.records.get(Uu);this.scope=null!=a?a.value:null,this.injectorDefTypes.forEach((function(e){return o.get(e)})),this.source=r||("object"==typeof e?null:B(e))}return Object.defineProperty(t.prototype,"destroyed",{get:function(){return this._destroyed},enumerable:!0,configurable:!0}),t.prototype.destroy=function(){this.assertNotDestroyed(),this._destroyed=!0;try{this.onDestroy.forEach((function(e){return e.ngOnDestroy()}))}finally{this.records.clear(),this.onDestroy.clear(),this.injectorDefTypes.clear()}},t.prototype.get=function(t,n,r){void 0===n&&(n=de),void 0===r&&(r=e.InjectFlags.Default),this.assertNotDestroyed();var o=we(this);try{if(!(r&e.InjectFlags.SkipSelf)){var i=this.records.get(t);if(void 0===i){var a=function u(e){return"function"==typeof e||"object"==typeof e&&e instanceof ce}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */(t)&&A(t);i=a&&this.injectableDefInScope(a)?$u(Ju(t),Qu):null,this.records.set(t,i)}if(null!=i)return this.hydrate(t,i)}return(r&e.InjectFlags.Self?Wu():this.parent).get(t,n=r&e.InjectFlags.Optional&&n===de?null:n)}catch(e){if("NullInjectorError"===e.name){if((e[pe]=e[pe]||[]).unshift(B(t)),o)throw e;return Ne(e,t,"R3InjectorError",this.source)}throw e}finally{we(o)}},t.prototype.toString=function(){var e=[];return this.records.forEach((function(t,n){return e.push(B(n))})),"R3Injector["+e.join(", ")+"]"},t.prototype.assertNotDestroyed=function(){if(this._destroyed)throw new Error("Injector has already been destroyed.")},t.prototype.processInjectorType=function(e,t,n){var r=this;if(!(e=q(e)))return!1;var o=R(e),i=null==o&&e.ngModule||void 0,a=void 0===i?e:i,u=-1!==n.indexOf(a);if(void 0!==i&&(o=R(i)),null==o)return!1;if(this.injectorDefTypes.add(a),this.records.set(a,$u(o.factory,Qu)),null!=o.imports&&!u){var s;n.push(a);try{He(o.imports,(function(e){r.processInjectorType(e,t,n)&&(void 0===s&&(s=[]),s.push(e))}))}finally{}if(void 0!==s)for(var l=function(e){var t=s[e],n=t.ngModule,o=t.providers;He(o,(function(e){return r.processProvider(e,n,o||qu)}))},c=0;c<s.length;c++)l(c)}var f=o.providers;if(null!=f&&!u){var d=e;He(f,(function(e){return r.processProvider(e,d,f)}))}return void 0!==i&&void 0!==e.providers},t.prototype.processProvider=function(e,t,n){var r=es(e=q(e))?e:q(e&&e.provide),o=function i(e,t,n){return Xu(e)?$u(void 0,e.useValue):$u(Yu(e,t,n),Qu)}(e,t,n);if(es(e)||!0!==e.multi){var a=this.records.get(r);a&&void 0!==a.multi&&ti()}else{var u=this.records.get(r);u?void 0===u.multi&&ti():((u=$u(void 0,Qu,!0)).factory=function(){return Oe(u.multi)},this.records.set(r,u)),r=e,u.multi.push(e)}this.records.set(r,o)},t.prototype.hydrate=function(e,t){return t.value===zu?function n(e){throw new Error("Cannot instantiate cyclic dependency! "+e)}(B(e)):t.value===Qu&&(t.value=zu,t.value=t.factory()),"object"==typeof t.value&&t.value&&function r(e){return null!==e&&"object"==typeof e&&"function"==typeof e.ngOnDestroy}(t.value)&&this.onDestroy.add(t.value),t.value},t.prototype.injectableDefInScope=function(e){return!!e.providedIn&&("string"==typeof e.providedIn?"any"===e.providedIn||e.providedIn===this.scope:this.injectorDefTypes.has(e.providedIn))},t}();function Ju(e){var t=A(e),n=null!==t?t.factory:lt(e);if(null!==n)return n;var r=R(e);if(null!==r)return r.factory;if(e instanceof ce)throw new Error("Token "+B(e)+" is missing a ɵprov definition.");if(e instanceof Function)return function o(e){var t=e.length;if(t>0){var n=Ue(t,"?");throw new Error("Can't resolve all parameters for "+B(e)+": ("+n.join(", ")+").")}var r=function o(e){var t=e&&(e[V]||e[H]||e[M]&&e[M]());if(t){var n=function r(e){if(e.hasOwnProperty("name"))return e.name;var t=(""+e).match(/^function\s*([^\s(]+)/);return null===t?"":t[1]}(e);return console.warn('DEPRECATED: DI is instantiating a token "'+n+'" that inherits its @Injectable decorator but does not provide one itself.\nThis will become an error in v10. Please add @Injectable() to the "'+n+'" class.'),t}return null}(e);return null!==r?function(){return r.factory(e)}:function(){return new e}}(e);throw new Error("unreachable")}function Yu(e,t,n){var r=void 0;if(es(e))return Ju(q(e));if(Xu(e))r=function(){return q(e.useValue)};else if(function o(e){return!(!e||!e.useFactory)}(e))r=function(){return e.useFactory.apply(e,f(Oe(e.deps||[])))};else if(function i(e){return!(!e||!e.useExisting)}(e))r=function(){return Ie(q(e.useExisting))};else{var a=q(e&&(e.useClass||e.provide));if(a||function u(e,t,n){var r="";throw e&&t&&(r=" - only instances of Provider and Type are allowed, got: ["+t.map((function(e){return e==n?"?"+n+"?":"..."})).join(", ")+"]"),new Error("Invalid provider for the NgModule '"+B(e)+"'"+r)}(t,n,e),!function s(e){return!!e.deps}(e))return Ju(a);r=function(){return new(a.bind.apply(a,f([void 0],Oe(e.deps))))}}return r}function $u(e,t,n){return void 0===n&&(n=!1),{factory:e,value:t,multi:n?[]:void 0}}function Xu(e){return null!==e&&"object"==typeof e&&me in e}function es(e){return"function"==typeof e}var ts=function ns(e,t,n){return new ss(e,t,n)},rs=function(){function e(){}return e.create=function(e,t){return Array.isArray(e)?ts(e,t,""):ts(e.providers,e.parent,e.name||"")},e.THROW_IF_NOT_FOUND=de,e.NULL=new Pe,e.ɵprov=T({token:e,providedIn:"any",factory:function(){return Ie(fe)}}),e.__NG_ELEMENT_ID__=-1,e}(),os=function(e){return e},is=[],as=os,us=function(){return Array.prototype.slice.call(arguments)},ss=function(){function t(e,t,n){void 0===t&&(t=rs.NULL),void 0===n&&(n=null),this.parent=t,this.source=n;var r=this._records=new Map;r.set(rs,{token:rs,fn:os,deps:is,value:this,useNew:!1}),r.set(fe,{token:fe,fn:os,deps:is,value:this,useNew:!1}),this.scope=function e(t,n){var r=null;if(n)if(n=q(n),Array.isArray(n))for(var o=0;o<n.length;o++)r=e(t,n[o])||r;else{if("function"==typeof n)throw fs("Function/Class not supported",n);if(!n||"object"!=typeof n||!n.provide)throw fs("Unexpected provider",n);var i=q(n.provide),a=ls(n);if(!0===n.multi){var u=t.get(i);if(u){if(u.fn!==us)throw cs(i)}else t.set(i,u={token:n.provide,deps:[],useNew:!1,fn:us,value:is});u.deps.push({token:i=n,options:6})}var s=t.get(i);if(s&&s.fn==us)throw cs(i);i===Uu&&(r=a.value),t.set(i,a)}return r}(r,e)}return t.prototype.get=function(t,n,r){void 0===r&&(r=e.InjectFlags.Default);var o=this._records,i=o.get(t);if(void 0===i){var a=A(t);if(a){var u=a&&a.providedIn;("any"===u||null!=u&&u===this.scope)&&o.set(t,i=ls({provide:t,useFactory:a.factory,deps:is}))}void 0===i&&o.set(t,null)}var s=we(this);try{return function t(n,r,o,i,a,u){try{return function s(n,r,o,i,a,u){var s,l;if(!r||u&e.InjectFlags.SkipSelf)l=u&e.InjectFlags.Self?rs.NULL.get(n,u&e.InjectFlags.Optional?void 0!==a?a:null:a):i.get(n,a,e.InjectFlags.Default);else{if((l=r.value)==as)throw Error("ɵCircular dependency");if(l===is){r.value=as;var c=r.useNew,d=r.fn,p=r.deps,h=is;if(p.length){h=[];for(var v=0;v<p.length;v++){var y=p[v],g=y.options,m=2&g?o.get(y.token):void 0;h.push(t(y.token,m,o,m||4&g?i:rs.NULL,1&g?null:rs.THROW_IF_NOT_FOUND,e.InjectFlags.Default))}}r.value=l=c?new((s=d).bind.apply(s,f([void 0],h))):d.apply(void 0,h)}}return l}(n,r,o,i,a,u)}catch(e){throw e instanceof Error||(e=new Error(e)),(e[pe]=e[pe]||[]).unshift(n),r&&r.value==as&&(r.value=is),e}}(t,i,o,this.parent,n,r)}catch(e){return Ne(e,t,"StaticInjectorError",this.source)}finally{we(s)}},t.prototype.toString=function(){var e=[];return this._records.forEach((function(t,n){return e.push(B(n))})),"StaticInjector["+e.join(", ")+"]"},t}();function ls(e){var t=function n(e){var t=is,n=e.deps;if(n&&n.length){t=[];for(var r=0;r<n.length;r++){var o=6,i=q(n[r]);if(Array.isArray(i))for(var a=0,u=i;a<u.length;a++){var s=u[a];s instanceof C||s==C?o|=1:s instanceof x||s==x?o&=-3:s instanceof I||s==I?o&=-5:i=s instanceof b?s.token:q(s)}t.push({token:i,options:o})}}else if(e.useExisting)t=[{token:i=q(e.useExisting),options:6}];else if(!(n||me in e))throw fs("'deps' required",e);return t}(e),r=os,o=is,i=!1,a=q(e.provide);if(me in e)o=e.useValue;else if(e.useFactory)r=e.useFactory;else if(e.useExisting);else if(e.useClass)i=!0,r=q(e.useClass);else{if("function"!=typeof a)throw fs("StaticProvider does not have [useValue|useFactory|useExisting|useClass] or [provide] is not newable",e);i=!0,r=a}return{deps:t,fn:r,useNew:i,value:o}}function cs(e){return fs("Cannot mix multi providers and regular providers",e)}function fs(e,t){return new Error(Te(e,t,"StaticInjectorError"))}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function ds(e){return e.length>1?" ("+function t(e){for(var t=[],n=0;n<e.length;++n){if(t.indexOf(e[n])>-1)return t.push(e[n]),t;t.push(e[n])}return t}(e.slice().reverse()).map((function(e){return B(e.token)})).join(" -> ")+")":""}function ps(e,t,n,r){var o=[t],i=n(o),a=r?function u(e,t){var n=e+" caused by: "+(t instanceof Error?t.message:t),r=Error(n);return r[Ir]=t,r}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */(i,r):Error(i);return a.addKey=hs,a.keys=o,a.injectors=[e],a.constructResolvingMessage=n,a[Ir]=r,a}function hs(e,t){this.injectors.push(e),this.keys.push(t),this.message=this.constructResolvingMessage(this.keys)}function vs(e,t){for(var n=[],r=0,o=t.length;r<o;r++){var i=t[r];n.push(i&&0!=i.length?i.map(B).join(" "):"?")}return Error("Cannot resolve all parameters for '"+B(e)+"'("+n.join(", ")+"). Make sure that all the parameters are decorated with Inject or have valid type annotations and that '"+B(e)+"' is decorated with Injectable.")}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
var ys=function(){function e(e,t){if(this.token=e,this.id=t,!e)throw new Error("Token must be defined!");this.displayName=B(this.token)}return e.get=function(e){return gs.get(q(e))},Object.defineProperty(e,"numberOfKeys",{get:function(){return gs.numberOfKeys},enumerable:!0,configurable:!0}),e}(),gs=new(function(){function e(){this._allKeys=new Map}return e.prototype.get=function(e){if(e instanceof ys)return e;if(this._allKeys.has(e))return this._allKeys.get(e);var t=new ys(e,ys.numberOfKeys);return this._allKeys.set(e,t),t},Object.defineProperty(e.prototype,"numberOfKeys",{get:function(){return this._allKeys.size},enumerable:!0,configurable:!0}),e}()),ms=new(function(){function e(e){this.reflectionCapabilities=e}return e.prototype.updateCapabilities=function(e){this.reflectionCapabilities=e},e.prototype.factory=function(e){return this.reflectionCapabilities.factory(e)},e.prototype.parameters=function(e){return this.reflectionCapabilities.parameters(e)},e.prototype.annotations=function(e){return this.reflectionCapabilities.annotations(e)},e.prototype.propMetadata=function(e){return this.reflectionCapabilities.propMetadata(e)},e.prototype.hasLifecycleHook=function(e,t){return this.reflectionCapabilities.hasLifecycleHook(e,t)},e.prototype.getter=function(e){return this.reflectionCapabilities.getter(e)},e.prototype.setter=function(e){return this.reflectionCapabilities.setter(e)},e.prototype.method=function(e){return this.reflectionCapabilities.method(e)},e.prototype.importUri=function(e){return this.reflectionCapabilities.importUri(e)},e.prototype.resourceUri=function(e){return this.reflectionCapabilities.resourceUri(e)},e.prototype.resolveIdentifier=function(e,t,n,r){return this.reflectionCapabilities.resolveIdentifier(e,t,n,r)},e.prototype.resolveEnum=function(e,t){return this.reflectionCapabilities.resolveEnum(e,t)},e}())(new Eu),_s=function(){function e(e,t,n){this.key=e,this.optional=t,this.visibility=n}return e.fromKey=function(t){return new e(t,!1,null)},e}(),ws=[],bs=function bs(e,t,n){this.key=e,this.resolvedFactories=t,this.multiProvider=n,this.resolvedFactory=this.resolvedFactories[0]},Cs=function Cs(e,t){this.factory=e,this.dependencies=t};function Is(e){var t,n;if(e.useClass){var r=q(e.useClass);t=ms.factory(r),n=ks(r)}else e.useExisting?(t=function(e){return e},n=[_s.fromKey(ys.get(e.useExisting))]):e.useFactory?(t=e.useFactory,n=function o(e,t){if(t){var n=t.map((function(e){return[e]}));return t.map((function(t){return Os(e,t,n)}))}return ks(e)}(e.useFactory,e.deps)):(t=function(){return e.useValue},n=ws);return new Cs(t,n)}function xs(e){return new bs(ys.get(e.provide),[Is(e)],e.multi||!1)}function Es(e){var t=function n(e,t){for(var n=0;n<e.length;n++){var r=e[n],o=t.get(r.key.id);if(o){if(r.multiProvider!==o.multiProvider)throw Error("Cannot mix multi providers and regular providers, got: "+o+" "+r);if(r.multiProvider)for(var i=0;i<r.resolvedFactories.length;i++)o.resolvedFactories.push(r.resolvedFactories[i]);else t.set(r.key.id,r)}else{var a;a=r.multiProvider?new bs(r.key,r.resolvedFactories.slice(),r.multiProvider):r,t.set(r.key.id,a)}}return t}(function e(t,n){return t.forEach((function(t){if(t instanceof _u)n.push({provide:t,useClass:t});else if(t&&"object"==typeof t&&void 0!==t.provide)n.push(t);else{if(!Array.isArray(t))throw function r(e){return Error("Invalid provider - only instances of Provider and Type are allowed, got: "+e)}(t);e(t,n)}})),n}(e,[]).map(xs),new Map);return Array.from(t.values())}function ks(e){var t=ms.parameters(e);if(!t)return[];if(t.some((function(e){return null==e})))throw vs(e,t);return t.map((function(n){return Os(e,n,t)}))}function Os(e,t,n){var r=null,o=!1;if(!Array.isArray(t))return Ps(t instanceof b?t.token:t,o,null);for(var i=null,a=0;a<t.length;++a){var u=t[a];u instanceof _u?r=u:u instanceof b?r=u.token:u instanceof C?o=!0:u instanceof I||u instanceof x?i=u:u instanceof ce&&(r=u)}if(null!=(r=q(r)))return Ps(r,o,i);throw vs(e,n)}function Ps(e,t,n){return new _s(ys.get(e),t,n)}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var Ns,Ts={},js=function(){function e(){}return e.resolve=function(e){return Es(e)},e.resolveAndCreate=function(t,n){var r=e.resolve(t);return e.fromResolvedProviders(r,n)},e.fromResolvedProviders=function(e,t){return new Ds(e,t)},e}(),Ds=function(){function e(e,t){this._constructionCounter=0,this._providers=e,this.parent=t||null;var n=e.length;this.keyIds=[],this.objs=[];for(var r=0;r<n;r++)this.keyIds[r]=e[r].key.id,this.objs[r]=Ts}return e.prototype.get=function(e,t){return void 0===t&&(t=de),this._getByKey(ys.get(e),null,t)},e.prototype.resolveAndCreateChild=function(e){var t=js.resolve(e);return this.createChildFromResolved(t)},e.prototype.createChildFromResolved=function(t){var n=new e(t);return n.parent=this,n},e.prototype.resolveAndInstantiate=function(e){return this.instantiateResolved(js.resolve([e])[0])},e.prototype.instantiateResolved=function(e){return this._instantiateProvider(e)},e.prototype.getProviderAtIndex=function(e){if(e<0||e>=this._providers.length)throw function t(e){return Error("Index "+e+" is out-of-bounds.")}(e);return this._providers[e]},e.prototype._new=function(e){if(this._constructionCounter++>this._getMaxNumberOfObjects())throw function t(e,n){return ps(e,n,(function(e){return"Cannot instantiate cyclic dependency!"+ds(e)}))}(this,e.key);return this._instantiateProvider(e)},e.prototype._getMaxNumberOfObjects=function(){return this.objs.length},e.prototype._instantiateProvider=function(e){if(e.multiProvider){for(var t=[],n=0;n<e.resolvedFactories.length;++n)t[n]=this._instantiate(e,e.resolvedFactories[n]);return t}return this._instantiate(e,e.resolvedFactories[0])},e.prototype._instantiate=function(e,t){var n,r,o=this,i=t.factory;try{n=t.dependencies.map((function(e){return o._getByReflectiveDependency(e)}))}catch(t){throw t.addKey&&t.addKey(this,e.key),t}try{r=i.apply(void 0,f(n))}catch(t){throw function a(e,t,n,r){return ps(e,r,(function(e){var n=B(e[0].token);return t.message+": Error during instantiation of "+n+"!"+ds(e)+"."}),t)}(this,t,0,e.key)}return r},e.prototype._getByReflectiveDependency=function(e){return this._getByKey(e.key,e.visibility,e.optional?null:de)},e.prototype._getByKey=function(t,n,r){return t===e.INJECTOR_KEY?this:n instanceof I?this._getByKeySelf(t,r):this._getByKeyDefault(t,r,n)},e.prototype._getObjByKeyId=function(e){for(var t=0;t<this.keyIds.length;t++)if(this.keyIds[t]===e)return this.objs[t]===Ts&&(this.objs[t]=this._new(this._providers[t])),this.objs[t];return Ts},e.prototype._throwOrNull=function(e,t){if(t!==de)return t;throw function n(e,t){return ps(e,t,(function(e){return"No provider for "+B(e[0].token)+"!"+ds(e)}))}(this,e)},e.prototype._getByKeySelf=function(e,t){var n=this._getObjByKeyId(e.id);return n!==Ts?n:this._throwOrNull(e,t)},e.prototype._getByKeyDefault=function(t,n,r){var o;for(o=r instanceof x?this.parent:this;o instanceof e;){var i=o,a=i._getObjByKeyId(t.id);if(a!==Ts)return a;o=i.parent}return null!==o?o.get(t.token,n):this._throwOrNull(t,n)},Object.defineProperty(e.prototype,"displayName",{get:function(){return"ReflectiveInjector(providers: ["+function e(t,n){for(var r=[],o=0;o<t._providers.length;++o)r[o]=n(t.getProviderAtIndex(o));return r}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */(this,(function(e){return' "'+e.key.displayName+'" '})).join(", ")+"])"},enumerable:!0,configurable:!0}),e.prototype.toString=function(){return this.displayName},e.INJECTOR_KEY=ys.get(rs),e}(),As=new ce("AnalyzeForEntryComponents"),Ss=m("ContentChildren",(function(e,t){return void 0===t&&(t={}),i({selector:e,first:!1,isViewQuery:!1,descendants:!1},t)}),Ns=function Ns(){}),Rs=m("ContentChild",(function(e,t){return void 0===t&&(t={}),i({selector:e,first:!0,isViewQuery:!1,descendants:!0},t)}),Ns),Vs=m("ViewChildren",(function(e,t){return void 0===t&&(t={}),i({selector:e,first:!1,isViewQuery:!0,descendants:!0},t)}),Ns),Fs=m("ViewChild",(function(e,t){return i({selector:e,first:!0,isViewQuery:!0,descendants:!0},t)}),Ns);
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
function Ms(e){var t=[],n=new Map;function r(t){var r=n.get(t);if(!r){var o=e(t);n.set(t,r=o.then(Qs))}return r}return Hs.forEach((function(e,n){var o=[];e.templateUrl&&o.push(r(e.templateUrl).then((function(t){e.template=t})));var i=e.styleUrls,a=e.styles||(e.styles=[]),u=e.styles.length;i&&i.forEach((function(t,n){a.push(""),o.push(r(t).then((function(r){a[u+n]=r,i.splice(i.indexOf(t),1),0==i.length&&(e.styleUrls=void 0)})))}));var s=Promise.all(o).then((function(){return function e(t){Ls.delete(t)}
/**
    * @license
    * Copyright Google Inc. All Rights Reserved.
    *
    * Use of this source code is governed by an MIT-style license that can be
    * found in the LICENSE file at https://angular.io/license
    */(n)}));t.push(s)})),Us(),Promise.all(t).then((function(){}))}var Hs=new Map,Ls=new Set;function Bs(e){return!!(e.templateUrl&&!e.hasOwnProperty("template")||e.styleUrls&&e.styleUrls.length)}function Us(){var e=Hs;return Hs=new Map,e}function Qs(e){return"string"==typeof e?e:e.text()}function zs(e,t){for(var n=e.styles,r=e.classes,o=0,i=0;i<t.length;i++){var a=t[i];"number"==typeof a?o=a:1==o?r=U(r,a):2==o&&(n=U(n,a+": "+t[++i]+";"))}null!==n&&(e.styles=n),null!==r&&(e.classes=r)}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var qs=null;function Zs(){if(!qs){var e=ee.Symbol;if(e&&e.iterator)qs=e.iterator;else for(var t=Object.getOwnPropertyNames(Map.prototype),n=0;n<t.length;++n){var r=t[n];"entries"!==r&&"size"!==r&&Map.prototype[r]===Map.prototype.entries&&(qs=r)}}return qs}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
function Ws(e,t){return e===t||"number"==typeof e&&"number"==typeof t&&isNaN(e)&&isNaN(t)}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
function Ks(e,t){var n=Js(e),r=Js(t);return n&&r?function o(e,t,n){for(var r=e[Zs()](),o=t[Zs()]();;){var i=r.next(),a=o.next();if(i.done&&a.done)return!0;if(i.done||a.done)return!1;if(!n(i.value,a.value))return!1}}(e,t,Ks):!(n||!e||"object"!=typeof e&&"function"!=typeof e||r||!t||"object"!=typeof t&&"function"!=typeof t)||Ws(e,t)}var Gs=function(){function e(e){this.wrapped=e}return e.wrap=function(t){return new e(t)},e.unwrap=function(t){return e.isWrapped(t)?t.wrapped:t},e.isWrapped=function(t){return t instanceof e},e}();function Js(e){return!!Ys(e)&&(Array.isArray(e)||!(e instanceof Map)&&Zs()in e)}function Ys(e){return null!==e&&("function"==typeof e||"object"==typeof e)}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function $s(e,t,n){return e[t]=n}function Xs(e,t){return e[t]}function el(e,t,n){return!Object.is(e[t],n)&&(e[t]=n,!0)}function tl(e,t,n,r){var o=el(e,t,n);return el(e,t+1,r)||o}function nl(e,t,n,r,o){var i=tl(e,t,n,r);return el(e,t+2,o)||i}function rl(e,t,n,r,o,i){var a=tl(e,t,n,r);return tl(e,t+2,o,i)||a}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function ol(e,t,n,r){var o=Kt();return el(o,un(),t)&&ha(_n(),e,t,Gt(),o,n,r),ol}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function il(e,t){for(var n=!1,r=on(),o=1;o<t.length;o+=2)n=el(e,r++,t[o])||n;if(an(r),!n)return pi;var i=t[0];for(o=1;o<t.length;o+=2)i+=Gn(t[o])+t[o+1];return i}function al(e,t,n,r){return el(e,un(),n)?t+Gn(n)+r:pi}function ul(e,t,n,r,o,i){var a=tl(e,on(),n,o);return sn(2),a?t+Gn(n)+r+Gn(o)+i:pi}function sl(e,t,n,r,o,i,a,u){var s=nl(e,on(),n,o,a);return sn(3),s?t+Gn(n)+r+Gn(o)+i+Gn(a)+u:pi}function ll(e,t,n,r,o,i,a,u,s,l){var c=rl(e,on(),n,o,a,s);return sn(4),c?t+Gn(n)+r+Gn(o)+i+Gn(a)+u+Gn(s)+l:pi}function cl(e,t,n,r,o,i,a,u,s,l,c,f){var d=on(),p=rl(e,d,n,o,a,s);return p=el(e,d+4,c)||p,sn(5),p?t+Gn(n)+r+Gn(o)+i+Gn(a)+u+Gn(s)+l+Gn(c)+f:pi}function fl(e,t,n,r,o,i,a,u,s,l,c,f,d,p){var h=on(),v=rl(e,h,n,o,a,s);return v=tl(e,h+4,c,d)||v,sn(6),v?t+Gn(n)+r+Gn(o)+i+Gn(a)+u+Gn(s)+l+Gn(c)+f+Gn(d)+p:pi}function dl(e,t,n,r,o,i,a,u,s,l,c,f,d,p,h,v){var y=on(),g=rl(e,y,n,o,a,s);return g=nl(e,y+4,c,d,h)||g,sn(7),g?t+Gn(n)+r+Gn(o)+i+Gn(a)+u+Gn(s)+l+Gn(c)+f+Gn(d)+p+Gn(h)+v:pi}function pl(e,t,n,r,o,i,a,u,s,l,c,f,d,p,h,v,y,g){var m=on(),_=rl(e,m,n,o,a,s);return _=rl(e,m+4,c,d,h,y)||_,sn(8),_?t+Gn(n)+r+Gn(o)+i+Gn(a)+u+Gn(s)+l+Gn(c)+f+Gn(d)+p+Gn(h)+v+Gn(y)+g:pi}function hl(e,t,n,r,o,i){var a=Kt(),u=al(a,t,n,r);return u!==pi&&ha(_n(),e,u,Gt(),a,o,i),hl}function vl(e,t,n,r,o,i,a,u){var s=Kt(),l=ul(s,t,n,r,o,i);return l!==pi&&ha(_n(),e,l,Gt(),s,a,u),vl}function yl(e,t,n,r,o,i,a,u,s,l){var c=Kt(),f=sl(c,t,n,r,o,i,a,u);return f!==pi&&ha(_n(),e,f,Gt(),c,s,l),yl}function gl(e,t,n,r,o,i,a,u,s,l,c,f){var d=Kt(),p=ll(d,t,n,r,o,i,a,u,s,l);return p!==pi&&ha(_n(),e,p,Gt(),d,c,f),gl}function ml(e,t,n,r,o,i,a,u,s,l,c,f,d,p){var h=Kt(),v=cl(h,t,n,r,o,i,a,u,s,l,c,f);return v!==pi&&ha(_n(),e,v,Gt(),h,d,p),ml}function _l(e,t,n,r,o,i,a,u,s,l,c,f,d,p,h,v){var y=Kt(),g=fl(y,t,n,r,o,i,a,u,s,l,c,f,d,p);return g!==pi&&ha(_n(),e,g,Gt(),y,h,v),_l}function wl(e,t,n,r,o,i,a,u,s,l,c,f,d,p,h,v,y,g){var m=Kt(),_=dl(m,t,n,r,o,i,a,u,s,l,c,f,d,p,h,v);return _!==pi&&ha(_n(),e,_,Gt(),m,y,g),wl}function bl(e,t,n,r,o,i,a,u,s,l,c,f,d,p,h,v,y,g,m,_){var w=Kt(),b=pl(w,t,n,r,o,i,a,u,s,l,c,f,d,p,h,v,y,g);return b!==pi&&ha(_n(),e,b,Gt(),w,m,_),bl}function Cl(e,t,n,r){var o=Kt(),i=il(o,t);if(i!==pi){var a=Gt();ha(_n(),e,i,a,o,n,r)}return Cl}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function Il(e){var t=Ko(e);xa(t[dt],t,e)}function xl(e){!function t(e,n){var r,o=0===e.flags;e.flags|=n,o&&e.clean==Hi&&(e.clean=new Promise((function(e){return r=e})),e.scheduler((function(){if(1&e.flags&&(e.flags&=-2,Ia(e)),2&e.flags){e.flags&=-3;var t=e.playerHandler;t&&t.flushPlayers()}e.clean=Hi,r(null)})))}(Ca(Ko(e))[_t],1)}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
function El(e){var t=Kt(),n=Gt(),r=function o(e,t,n,r,i){var a=n+Tt,u=t[a]=t[Ct].createComment(""),s=Qi(e,t[gt],n,0,r,i),l=t[a]=ga(u,t,u,s);return $a(e,t,u,s),Go(u,t),ba(t,l),s}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */(n,t,e,null,null);n.firstCreatePass&&(r.tViews=[]),en()}function kl(e,t,n,r,o,i,a,u){var s=Kt(),l=Gt(),c=e+Tt,f=l.firstCreatePass?function d(e,t,n,r,o,i,a,u,s){var l=t.consts,c=Qi(t,n[gt],e,0,a||null,Qo(l,u));oa(t,n,c,Qo(l,s)),xn(t,c);var f=c.tViews=Xi(2,-1,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,l),d=ta(0,null,2,-1,null,null);return d.injectorIndex=c.injectorIndex,f.node=d,null!==t.queries&&(t.queries.template(t,c),f.queries=t.queries.embeddedTView(c)),c}(e,l,s,t,n,r,o,i,a):l.data[c];$t(f,!1);var p=s[Ct].createComment("");$a(l,s,p,f),Go(p,s),ba(s,s[c]=ga(p,s,p,f)),Bt(f)&&Ji(l,s,f),null!=a&&Yi(s,f,u)}function Ol(e){var t=Kt(),n=Gt();if($t(Fo(n.data,e),!0),t[e+Tt][Dt]=0,!tn())if(3==(3&t[pt])){var r=n.preOrderCheckHooks;null!==r&&En(t,r,null)}else{var o=n.preOrderHooks;null!==o&&kn(t,o,0,null),On(t,0)}}function Pl(){var e=Yt();Xt()?en():$t(e=e.parent,!1);for(var t=Kt()[e.index],n=qo(t);n<t.length-Vt;)Ua(t,n)}function Nl(e,t,n,r){var o=n+Tt;o>=e.data.length&&(e.data[o]=null,e.blueprint[o]=null),t[o]=r}function Tl(e){return Fo(function t(){return zt.lFrame.contextLView}(),e)}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function jl(t,n){void 0===n&&(n=e.InjectFlags.Default);var r=Kt();return null==r?Ie(t,n):dr(Yt(),r,q(t),n)}function Dl(e){return fr(Yt(),e)}function Al(){throw new Error("invalid")}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function Sl(e,t,n){var r=Kt();if(el(r,un(),t)){var o=_n();ra(Gt(),r,o,e,t,n)}return Sl}function Rl(e,t,n,r,o){var i=t.inputs,a=o?"class":"style";Da(e,n,i[a]||o&&i.className,a,r)}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function Vl(e,t,n,r){var o=Kt(),i=Gt(),a=Tt+e,u=o[Ct],s=o[a]=Bi(t,u,function l(){return zt.lFrame.currentNamespace}()),c=i.firstCreatePass?function f(e,t,n,r,o,i,a){var u=t.consts,s=Qo(u,i),l=Qi(t,n[gt],e,3,o,s);return oa(t,n,l,Qo(u,a)),null!==l.mergedAttrs&&zs(l,l.mergedAttrs),null!==t.queries&&t.queries.elementStart(t,l),l}(e,i,o,0,t,n,r):i.data[a];$t(c,!0);var d=c.mergedAttrs;null!==d&&Ln(u,s,d);var p=c.classes;null!==p&&au(u,s,p);var h=c.styles;null!==h&&iu(u,s,h),$a(i,o,s,c),0===function v(){return zt.lFrame.elementDepthCount}()&&Go(s,o),function y(){zt.lFrame.elementDepthCount++}(),Bt(c)&&(Ji(i,o,c),Gi(i,c,o)),null!==r&&Yi(o,c)}function Fl(){var e=Yt();Xt()?en():$t(e=e.parent,!1);var t=e;!function n(){zt.lFrame.elementDepthCount--}();var r=Gt();r.firstCreatePass&&(xn(r,e),Ht(e)&&r.queries.elementEnd(e)),null!==t.classes&&function o(e){return 0!=(16&e.flags)}(t)&&Rl(r,t,Kt(),t.classes,!0),null!==t.styles&&function i(e){return 0!=(32&e.flags)}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */(t)&&Rl(r,t,Kt(),t.styles,!1)}function Ml(e,t,n,r){Vl(e,t,n,r),Fl()}function Hl(e,t,n){var r=Kt(),o=Gt(),i=e+Tt,a=o.firstCreatePass?
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
function u(e,t,n,r,o){var i=t.consts,a=Qo(i,r),u=Qi(t,n[gt],e,4,"ng-container",a);return null!==a&&zs(u,a),oa(t,n,u,Qo(i,o)),null!==t.queries&&t.queries.elementStart(t,u),u}(e,o,r,t,n):o.data[i];$t(a,!0);var s=r[i]=r[Ct].createComment("");$a(o,r,s,a),Go(s,r),Bt(a)&&(Ji(o,r,a),Gi(o,a,r)),null!=n&&Yi(r,a)}function Ll(){var e=Yt(),t=Gt();Xt()?en():$t(e=e.parent,!1),t.firstCreatePass&&(xn(t,e),Ht(e)&&t.queries.elementEnd(e))}function Bl(e,t,n){Hl(e,t,n),Ll()}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function Ul(e,t,n){var r=Kt(),o=Yt(),i=2===o.type?o.parent:o,a=r[i.index],u=function s(e,t,n){for(var r=t+Vt;r<e.length;r++){var o=e[r][dt].id;if(o===n)return e[r];if(!(o<n))break;Ua(e,r-Vt)}return null}(a,qo(a),e);if(u)!function l(){zt.lFrame.isParent=!0}(),hn(u,u[dt].node);else{u=Ui(r,function c(e,t,n,r){var o=Kt()[dt],i=r.tViews;return(e>=i.length||null==i[e])&&(i[e]=Xi(2,e,null,t,n,o.directiveRegistry,o.pipeRegistry,null,null,o.consts)),i[e]}(e,t,n,i),null,16,null,null);var f=Xt()?o:o&&o.parent;zi(u[dt],f,e,u),hn(u,u[dt].node)}return a&&(Bo(u)&&Ha(u[dt],u,a,qo(a)),a[Dt]+=2),Bo(u)?3:2}function Ql(){var e=Kt(),t=Gt(),n=e[gt],r=e[_t];Bo(e)&&qi(t,e,r),Zi(t,e,t.template,r),gn(),$t(n,!1)}function zl(){return Kt()}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function ql(e){return!!e&&"function"==typeof e.then}function Zl(e){return!!e&&"function"==typeof e.subscribe}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function Wl(e,t,n,r){void 0===n&&(n=!1);var o=Kt(),i=Gt(),a=Yt();return Gl(i,o,o[Ct],a,e,t,n,r),Wl}function Kl(e,t,n,r){void 0===n&&(n=!1);var o=Yt(),i=Kt(),a=Ta(o,i);return Gl(Gt(),i,a,o,e,t,n,r),Kl}function Gl(e,t,n,r,o,i,a,u){void 0===a&&(a=!1);var s=Bt(r),l=e.firstCreatePass&&(e.cleanup||(e.cleanup=[])),c=Pa(t),f=!0;if(3===r.type){var d=So(r,t),p=u?u(d):We,h=p.target||d,v=c.length,y=u?function(e){return u(Do(e[r.index])).target}:r.index;if(Mn(n)){var g=null;if(!u&&s&&(g=function m(e,t,n,r){var o=e.cleanup;if(null!=o)for(var i=0;i<o.length-1;i+=2){var a=o[i];if(a===n&&o[i+1]===r){var u=t[mt],s=o[i+2];return u.length>s?u[s]:null}"string"==typeof a&&(i+=2)}return null}(e,t,o,r.index)),null!==g)(g.__ngLastListenerFn__||g).__ngNextListenerFn__=i,g.__ngLastListenerFn__=i,f=!1;else{i=Yl(r,t,i,!1);var _=n.listen(p.name||h,o,i);c.push(i,_),l&&l.push(o,y,v,v+1)}}else i=Yl(r,t,i,!0),h.addEventListener(o,i,a),c.push(i),l&&l.push(o,y,v,a)}var w,b=r.outputs;if(f&&null!==b&&(w=b[o])){var C=w.length;if(C)for(var I=0;I<C;I+=2){var x=t[w[I]][w[I+1]].subscribe(i),E=c.length;c.push(i,x),l&&l.push(o,r.index,E,-(E+1))}}}function Jl(e,t,n){try{return!1!==t(n)}catch(t){return ja(e,t),!1}}function Yl(e,t,n,r){return function o(i){if(i===Function)return n;var a=2&e.flags?Mo(e.index,t):t;0==(32&t[pt])&&Ca(a);for(var u=Jl(t,n,i),s=o.__ngNextListenerFn__;s;)u=Jl(t,s,i)&&u,s=s.__ngNextListenerFn__;return r&&!1===u&&(i.preventDefault(),i.returnValue=!1),u}}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function $l(e){return void 0===e&&(e=1),mn(e)}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function Xl(e,t){for(var n=null,r=function o(e){var t=e.attrs;if(null!=t){var n=t.indexOf(5);if(0==(1&n))return t[n+1]}return null}(e),i=0;i<t.length;i++){var a=t[i];if("*"!==a){if(null===r?li(e,a,!0):ci(r,a))return i}else n=i}return n}function ec(e){var t=Kt()[Ot][gt];if(!t.projection)for(var n=t.projection=Ue(e?e.length:1,null),r=n.slice(),o=t.child;null!==o;){var i=e?Xl(o,e):0;null!==i&&(r[i]?r[i].projectionNext=o:n[i]=o,r[i]=o),o=o.next}}var tc=!1;function nc(e){tc=e}function rc(e,t,n){void 0===t&&(t=0);var r=Kt(),o=Gt(),i=Qi(o,r[gt],e,1,null,n||null);null===i.projection&&(i.projection=t),en(),tc||ru(o,r,i)}function oc(e,t,n){return ic(e,"",t,"",n),oc}function ic(e,t,n,r,o){var i=Kt(),a=al(i,t,n,r);if(a!==pi){var u=_n();ra(Gt(),i,u,e,a,o)}return ic}function ac(e,t,n,r,o,i,a){var u=Kt(),s=ul(u,t,n,r,o,i);if(s!==pi){var l=_n();ra(Gt(),u,l,e,s,a)}return ac}function uc(e,t,n,r,o,i,a,u,s){var l=Kt(),c=sl(l,t,n,r,o,i,a,u);if(c!==pi){var f=_n();ra(Gt(),l,f,e,c,s)}return uc}function sc(e,t,n,r,o,i,a,u,s,l,c){var f=Kt(),d=ll(f,t,n,r,o,i,a,u,s,l);if(d!==pi){var p=_n();ra(Gt(),f,p,e,d,c)}return sc}function lc(e,t,n,r,o,i,a,u,s,l,c,f,d){var p=Kt(),h=cl(p,t,n,r,o,i,a,u,s,l,c,f);if(h!==pi){var v=_n();ra(Gt(),p,v,e,h,d)}return lc}function cc(e,t,n,r,o,i,a,u,s,l,c,f,d,p,h){var v=Kt(),y=fl(v,t,n,r,o,i,a,u,s,l,c,f,d,p);if(y!==pi){var g=_n();ra(Gt(),v,g,e,y,h)}return cc}function fc(e,t,n,r,o,i,a,u,s,l,c,f,d,p,h,v,y){var g=Kt(),m=dl(g,t,n,r,o,i,a,u,s,l,c,f,d,p,h,v);if(m!==pi){var _=_n();ra(Gt(),g,_,e,m,y)}return fc}function dc(e,t,n,r,o,i,a,u,s,l,c,f,d,p,h,v,y,g,m){var _=Kt(),w=pl(_,t,n,r,o,i,a,u,s,l,c,f,d,p,h,v,y,g);if(w!==pi){var b=_n();ra(Gt(),_,b,e,w,m)}return dc}function pc(e,t,n){var r=Kt(),o=il(r,t);if(o!==pi){var i=_n();ra(Gt(),r,i,e,o,n)}return pc}
/**
    * @license
    * Copyright Google Inc. All Rights Reserved.
    *
    * Use of this source code is governed by an MIT-style license that can be
    * found in the LICENSE file at https://angular.io/license
    */var hc=[];function vc(e,t,n,r,o){for(var i=e[n+1],a=null===t,u=r?Ci(i):Ei(i),s=!1;0!==u&&(!1===s||a);){var l=e[u+1];yc(e[u],t)&&(s=!0,e[u+1]=r?Pi(l):xi(l)),u=r?Ci(l):Ei(l)}s&&(e[n+1]=r?xi(i):Pi(i))}function yc(e,t){return null===e||null==t||(Array.isArray(e)?e[1]:e)===t||!(!Array.isArray(e)||"string"!=typeof t)&&qe(e,t)>=0}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var gc={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function mc(e){return e.substring(gc.key,gc.keyEnd)}function _c(e){return e.substring(gc.value,gc.valueEnd)}function wc(e,t){var n=gc.textEnd;return n===t?-1:(t=gc.keyEnd=function r(e,t,n){for(;t<n&&e.charCodeAt(t)>32;)t++;return t}(e,gc.key=t,n),Ic(e,t,n))}function bc(e,t){var n=gc.textEnd,r=gc.key=Ic(e,t,n);return n===r?-1:(r=gc.keyEnd=function o(e,t,n){for(var r;t<n&&(45===(r=e.charCodeAt(t))||95===r||(-33&r)>=65&&(-33&r)<=90);)t++;return t}(e,r,n),r=xc(e,r,n),r=gc.value=Ic(e,r,n),r=gc.valueEnd=function i(e,t,n){for(var r=-1,o=-1,i=-1,a=t,u=a;a<n;){var s=e.charCodeAt(a++);if(59===s)return u;34===s||39===s?u=a=Ec(e,s,a,n):t===a-4&&85===i&&82===o&&76===r&&40===s?u=a=Ec(e,41,a,n):s>32&&(u=a),i=o,o=r,r=-33&s}return u}(e,r,n),xc(e,r,n))}function Cc(e){gc.key=0,gc.keyEnd=0,gc.value=0,gc.valueEnd=0,gc.textEnd=e.length}function Ic(e,t,n){for(;t<n&&e.charCodeAt(t)<=32;)t++;return t}function xc(e,t,n,r){return(t=Ic(e,t,n))<n&&t++,t}function Ec(e,t,n,r){for(var o=-1,i=n;i<r;){var a=e.charCodeAt(i++);if(a==t&&92!==o)return i;o=92==a&&92===o?0:a}throw new Error}
/**
    * @license
    * Copyright Google Inc. All Rights Reserved.
    *
    * Use of this source code is governed by an MIT-style license that can be
    * found in the LICENSE file at https://angular.io/license
    */
function kc(e){!function t(e){zt.lFrame.currentSanitizer=e}(e)}function Oc(e,t,n){return Ac(e,t,n,!1),Oc}function Pc(e,t){return Ac(e,t,null,!0),Pc}function Nc(e){Sc(Hc,Tc,e,!1)}function Tc(e,t){for(var n=function r(e){return Cc(e),bc(e,Ic(e,0,gc.textEnd))}(t);n>=0;n=bc(t,n))Hc(e,mc(t),_c(t))}function jc(e){Sc(Qe,Dc,e,!0)}function Dc(e,t){for(var n=function r(e){return Cc(e),wc(e,Ic(e,0,gc.textEnd))}(t);n>=0;n=wc(t,n))Qe(e,mc(t),!0)}function Ac(e,t,n,r){var o=Kt(),i=Gt(),a=sn(2);if(i.firstUpdatePass&&Vc(i,e,a,r),t!==pi&&el(o,a,t)){var u=void 0;null==n&&(u=function s(){var e=zt.lFrame;return null===e?null:e.currentSanitizer}())&&(n=u),Lc(i,i.data[_n()+Tt],o,o[Ct],e,o[a+1]=function l(e,t){return null==e||("function"==typeof t?e=t(e):"string"==typeof t?e+=t:"object"==typeof e&&(e=B(Rr(e)))),e}(t,n),r,a)}}function Sc(e,t,n,r){var o=Gt(),i=sn(2);o.firstUpdatePass&&Vc(o,null,i,r);var a=Kt();if(n!==pi&&el(a,i,n)){var u=o.data[_n()+Tt];Qc(u,r)&&!Rc(o,i)?("string"==typeof n&&(n=U(r?u.classes:u.styles,n)),Rl(o,u,a,n,r)):function l(e,t,n,r,o,i,a,u){o===pi&&(o=hc);for(var s=0,l=0,c=0<o.length?o[0]:null,f=0<i.length?i[0]:null;null!==c||null!==f;){var d=s<o.length?o[s+1]:void 0,p=l<i.length?i[l+1]:void 0,h=null,v=void 0;c===f?(s+=2,l+=2,d!==p&&(h=f,v=p)):null===f||null!==c&&c<f?(s+=2,h=c):(l+=2,h=f,v=p),null!==h&&Lc(e,t,n,r,h,v,a,u),c=s<o.length?o[s]:null,f=l<i.length?i[l]:null}}(o,u,a,a[Ct],a[i+1],a[i+1]=function s(e,t,n){if(null==n||""===n)return hc;var r=[];if(Array.isArray(n))for(var o=0;o<n.length;o++)e(r,n[o],!0);else if("object"==typeof n)if(n instanceof Map)n.forEach((function(t,n){return e(r,n,t)}));else if(n instanceof Set)n.forEach((function(t){return e(r,t,!0)}));else for(var i in n)n.hasOwnProperty(i)&&e(r,i,n[i]);else"string"==typeof n&&t(r,n);return r}(e,t,n),r,i)}}function Rc(e,t){return t>=e.expandoStartIndex}function Vc(e,t,n,r){var o=e.data;if(null===o[n+1]){var i=o[_n()+Tt],a=Rc(e,n);Qc(i,r)&&null===t&&!a&&(t=!1),t=function u(e,t,n,r){var o=function i(e){var t=function n(){return zt.lFrame.currentDirectiveIndex}();return-1===t?null:e[t]}(e),a=r?t.residualClasses:t.residualStyles;if(null===o)0===(r?t.classBindings:t.styleBindings)&&(n=Mc(n=Fc(null,e,t,n,r),t.attrs,r),a=null);else{var u=t.directiveStylingLast;if(-1===u||e[u]!==o)if(n=Fc(o,e,t,n,r),null===a){var s=function l(e,t,n){var r=n?t.classBindings:t.styleBindings;if(0!==Ei(r))return e[Ci(r)]}(e,t,r);void 0!==s&&Array.isArray(s)&&function c(e,t,n,r){e[Ci(n?t.classBindings:t.styleBindings)]=r}(e,t,r,s=Mc(s=Fc(null,e,t,s[1],r),t.attrs,r))}else a=function f(e,t,n){for(var r=void 0,o=t.directiveEnd,i=1+t.directiveStylingLast;i<o;i++)r=Mc(r,e[i].hostAttrs,n);return Mc(r,t.attrs,n)}(e,t,r)}return void 0!==a&&(r?t.residualClasses=a:t.residualStyles=a),n}(o,i,t,r),function s(e,t,n,r,o,i){var a=i?t.classBindings:t.styleBindings,u=Ci(a),s=Ei(a);e[r]=n;var l,c=!1;if(Array.isArray(n)?(null===(l=n[1])||qe(n,l)>0)&&(c=!0):l=n,o)if(0!==s){var f=Ci(e[u+1]);e[r+1]=bi(f,u),0!==f&&(e[f+1]=ki(e[f+1],r)),e[u+1]=function d(e,t){return 131071&e|t<<17}(e[u+1],r)}else e[r+1]=bi(u,0),0!==u&&(e[u+1]=ki(e[u+1],r)),u=r;else e[r+1]=bi(s,0),0===u?u=r:e[s+1]=ki(e[s+1],r),s=r;c&&(e[r+1]=xi(e[r+1])),vc(e,l,r,!0),vc(e,l,r,!1),function p(e,t,n,r,o){var i=o?e.residualClasses:e.residualStyles;null!=i&&"string"==typeof t&&qe(i,t)>=0&&(n[r+1]=Pi(n[r+1]))}(t,l,e,r,i),a=bi(u,s),i?t.classBindings=a:t.styleBindings=a}(o,i,t,n,a,r)}}function Fc(e,t,n,r,o){var i=null,a=n.directiveEnd,u=n.directiveStylingLast;for(-1===u?u=n.directiveStart:u++;u<a&&(r=Mc(r,(i=t[u]).hostAttrs,o),i!==e);)u++;return null!==e&&(n.directiveStylingLast=u),r}function Mc(e,t,n){var r=n?1:2,o=-1;if(null!==t)for(var i=0;i<t.length;i++){var a=t[i];"number"==typeof a?o=a:o===r&&(Array.isArray(e)||(e=void 0===e?[]:["",e]),Qe(e,a,!!n||t[++i]))}return void 0===e?null:e}function Hc(e,t,n){Oo(t)&&(n=wo(n)),Qe(e,t,n)}function Lc(e,t,n,r,o,i,a,u){if(3===t.type){var s=e.data,l=s[u+1];Uc(Oi(l)?Bc(s,t,n,o,Ei(l),a):void 0)||(Uc(i)||Ii(l)&&(i=Bc(s,null,n,o,u,a)),function c(e,t,n,r,o){var i=Mn(e);if(t)o?i?e.addClass(n,r):n.classList.add(r):i?e.removeClass(n,r):n.classList.remove(r);else{var a=-1==r.indexOf("-")?void 0:2;null==o?i?e.removeStyle(n,r,a):n.style.removeProperty(r):i?e.setStyle(n,r,o,a):n.style.setProperty(r,o)}}(r,a,Ao(_n(),n),o,i))}}function Bc(e,t,n,r,o,i){for(var a=null===t,u=void 0;o>0;){var s=e[o],l=Array.isArray(s),c=l?s[1]:s,f=null===c,d=n[o+1];d===pi&&(d=f?hc:void 0);var p=f?ze(d,r):c===r?d:void 0;if(l&&!Uc(p)&&(p=ze(s,r)),Uc(p)&&(u=p,a))return u;var h=e[o+1];o=a?Ci(h):Ei(h)}if(null!==t){var v=i?t.residualClasses:t.residualStyles;null!=v&&(u=ze(v,r))}return u}function Uc(e){return void 0!==e}function Qc(e,t){return 0!=(e.flags&(t?16:32))}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function zc(e,t){void 0===t&&(t="");var n=Kt(),r=Gt(),o=e+Tt,i=r.firstCreatePass?Qi(r,n[gt],e,3,null,null):r.data[o],a=n[o]=Fa(t,n[Ct]);$a(r,n,a,i),$t(i,!1)}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function qc(e){return Zc("",e,""),qc}function Zc(e,t,n){var r=Kt(),o=al(r,e,t,n);return o!==pi&&Aa(r,_n(),o),Zc}function Wc(e,t,n,r,o){var i=Kt(),a=ul(i,e,t,n,r,o);return a!==pi&&Aa(i,_n(),a),Wc}function Kc(e,t,n,r,o,i,a){var u=Kt(),s=sl(u,e,t,n,r,o,i,a);return s!==pi&&Aa(u,_n(),s),Kc}function Gc(e,t,n,r,o,i,a,u,s){var l=Kt(),c=ll(l,e,t,n,r,o,i,a,u,s);return c!==pi&&Aa(l,_n(),c),Gc}function Jc(e,t,n,r,o,i,a,u,s,l,c){var f=Kt(),d=cl(f,e,t,n,r,o,i,a,u,s,l,c);return d!==pi&&Aa(f,_n(),d),Jc}function Yc(e,t,n,r,o,i,a,u,s,l,c,f,d){var p=Kt(),h=fl(p,e,t,n,r,o,i,a,u,s,l,c,f,d);return h!==pi&&Aa(p,_n(),h),Yc}function $c(e,t,n,r,o,i,a,u,s,l,c,f,d,p,h){var v=Kt(),y=dl(v,e,t,n,r,o,i,a,u,s,l,c,f,d,p,h);return y!==pi&&Aa(v,_n(),y),$c}function Xc(e,t,n,r,o,i,a,u,s,l,c,f,d,p,h,v,y){var g=Kt(),m=pl(g,e,t,n,r,o,i,a,u,s,l,c,f,d,p,h,v,y);return m!==pi&&Aa(g,_n(),m),Xc}function ef(e){var t=Kt(),n=il(t,e);return n!==pi&&Aa(t,_n(),n),ef}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function tf(e,t,n){Sc(Qe,Dc,al(Kt(),e,t,n),!0)}function nf(e,t,n,r,o){Sc(Qe,Dc,ul(Kt(),e,t,n,r,o),!0)}function rf(e,t,n,r,o,i,a){Sc(Qe,Dc,sl(Kt(),e,t,n,r,o,i,a),!0)}function of(e,t,n,r,o,i,a,u,s){Sc(Qe,Dc,ll(Kt(),e,t,n,r,o,i,a,u,s),!0)}function af(e,t,n,r,o,i,a,u,s,l,c){Sc(Qe,Dc,cl(Kt(),e,t,n,r,o,i,a,u,s,l,c),!0)}function uf(e,t,n,r,o,i,a,u,s,l,c,f,d){Sc(Qe,Dc,fl(Kt(),e,t,n,r,o,i,a,u,s,l,c,f,d),!0)}function sf(e,t,n,r,o,i,a,u,s,l,c,f,d,p,h){Sc(Qe,Dc,dl(Kt(),e,t,n,r,o,i,a,u,s,l,c,f,d,p,h),!0)}function lf(e,t,n,r,o,i,a,u,s,l,c,f,d,p,h,v,y){Sc(Qe,Dc,pl(Kt(),e,t,n,r,o,i,a,u,s,l,c,f,d,p,h,v,y),!0)}function cf(e){Sc(Qe,Dc,il(Kt(),e),!0)}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function ff(e,t,n,r,o){return Ac(e,al(Kt(),t,n,r),o,!1),ff}function df(e,t,n,r,o,i,a){return Ac(e,ul(Kt(),t,n,r,o,i),a,!1),df}function pf(e,t,n,r,o,i,a,u,s){return Ac(e,sl(Kt(),t,n,r,o,i,a,u),s,!1),pf}function hf(e,t,n,r,o,i,a,u,s,l,c){return Ac(e,ll(Kt(),t,n,r,o,i,a,u,s,l),c,!1),hf}function vf(e,t,n,r,o,i,a,u,s,l,c,f,d){return Ac(e,cl(Kt(),t,n,r,o,i,a,u,s,l,c,f),d,!1),vf}function yf(e,t,n,r,o,i,a,u,s,l,c,f,d,p,h){return Ac(e,fl(Kt(),t,n,r,o,i,a,u,s,l,c,f,d,p),h,!1),yf}function gf(e,t,n,r,o,i,a,u,s,l,c,f,d,p,h,v,y){return Ac(e,dl(Kt(),t,n,r,o,i,a,u,s,l,c,f,d,p,h,v),y,!1),gf}function mf(e,t,n,r,o,i,a,u,s,l,c,f,d,p,h,v,y,g,m){return Ac(e,pl(Kt(),t,n,r,o,i,a,u,s,l,c,f,d,p,h,v,y,g),m,!1),mf}function _f(e,t,n){return Ac(e,il(Kt(),t),n,!1),_f}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function wf(e,t,n){var r=Kt();if(el(r,un(),t)){var o=_n();ra(Gt(),r,o,e,t,n,!0)}return wf}function bf(e,t,n){var r=Kt();if(el(r,un(),t)){var o=_n();ra(Gt(),r,o,e,t,n,!0,Ta)}return bf}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function Cf(e){Df(e);var t=Pf(e,!1);return null===t?null:(void 0===t.component&&(t.component=function n(e,t){var n=t[dt].data[e];return 2&n.flags?t[n.directiveStart]:null}(t.nodeIndex,t.lView)),t.component)}function If(e){Df(e);var t=Pf(e,!1);return null===t?null:t.lView[_t]}function xf(e){var t=Pf(e,!1);if(null===t)return null;for(var n,r=t.lView;null===r[ft]&&(n=hi(r));)r=n;return 512&r[pt]?null:r[_t]}function Ef(e){return f(vi(e).components)}function kf(e){var t=Pf(e,!1);return null===t?rs.NULL:new _r(t.lView[dt].data[t.nodeIndex],t.lView)}function Of(e){var t=Pf(e);return void 0===t.directives&&(t.directives=Xo(t.nodeIndex,t.lView,!1)),null===t.directives?[]:f(t.directives)}function Pf(e,t){void 0===t&&(t=!0);var n=Zo(e);if(!n&&t)throw new Error("Invalid ng target");return n}function Nf(e){return Zo(e).native}function Tf(e){Df(e);var t=Pf(e,!1);if(null===t)return[];var n=t.lView,r=n[mt],o=n[dt].cleanup,i=[];if(o&&r)for(var a=0;a<o.length;){var u=o[a++],s=o[a++];if("string"==typeof u){var l=u,c=Do(n[s]),f=r[o[a++]],d=o[a++];e==c&&i.push({element:e,name:l,callback:f,useCapture:"boolean"==typeof d&&d,type:"boolean"==typeof d||d>=0?"dom":"output"})}}return i.sort(jf),i}function jf(e,t){return e.name==t.name?0:e.name<t.name?-1:1}function Df(e){if("undefined"!=typeof Element&&!(e instanceof Element))throw new Error("Expecting instance of DOM Element")}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function Af(e){xl(e),Ef(e).forEach((function(e){return Il(e)}))}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var Sf="ng",Rf=!1;function Vf(e,t){if(("undefined"==typeof COMPILED||!COMPILED)&&ee){var n=ee[Sf];n||(n=ee[Sf]={}),n[e]=t}}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function Ff(e,t,n,r,o,i,a){var u=n[dt];n[0+Tt]=e;var s=Qi(u,null,0,3,null,null),l=s.mergedAttrs=t.hostAttrs;null!==l&&(zs(s,l),null!==e&&(Ln(o,e,l),null!==s.classes&&au(o,e,s.classes),null!==s.styles&&iu(o,e,s.styles)));var c=r.createRenderer(e,t);null!==e&&i&&(Mn(o)?o.setAttribute(e,"ng-version",i):e.setAttribute("ng-version",i));var f=Ui(n,$i(t),null,t.onPush?64:16,n[Tt],s,r,c,a);return u.firstCreatePass&&(cr(ar(s,n),u,t.type),la(u,s),fa(s,n.length,1)),ba(n,f),n[Tt]=f}function Mf(e,t,n,r,o){var i=n[dt],a=function u(e,t,n){var r=Yt();e.firstCreatePass&&(n.providersResolver&&n.providersResolver(n),sa(e,r,1),da(e,t,n));var o=yr(t,e,t.length-1,r);Go(o,t);var i=So(r,t);return i&&Go(i,t),o}(i,n,t);r.components.push(a),e[_t]=a,o&&o.forEach((function(e){return e(a,t)})),t.contentQueries&&t.contentQueries(1,a,n.length-1);var s=Yt();if(i.firstCreatePass&&(null!==t.hostBindings||null!==t.hostAttrs)){wn(s.index-Tt);var l=n[dt];ia(l,t),aa(l,n,t.hostVars),ua(t,a)}return a}function Hf(e,t){return{components:[],scheduler:e||Yn,clean:Oa,playerHandler:t||null,flags:0}}function Lf(e,t){var n=Lo(e)[dt],r=n.data.length-1;xn(n,{directiveStart:r,directiveEnd:r+1})}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
function Bf(e){return Object.getPrototypeOf(e.prototype).constructor}function Uf(e){for(var t=Bf(e.type),n=!0,r=[e];t;){var o=void 0;if(Ut(e))o=t.ɵcmp||t.ɵdir;else{if(t.ɵcmp)throw new Error("Directives cannot inherit Components");o=t.ɵdir}if(o){if(n){r.push(o);var i=e;i.inputs=Qf(e.inputs),i.declaredInputs=Qf(e.declaredInputs),i.outputs=Qf(e.outputs);var a=o.hostBindings;a&&Zf(e,a);var u=o.viewQuery,s=o.contentQueries;u&&zf(e,u),s&&qf(e,s),N(e.inputs,o.inputs),N(e.declaredInputs,o.declaredInputs),N(e.outputs,o.outputs),i.afterContentChecked=i.afterContentChecked||o.afterContentChecked,i.afterContentInit=e.afterContentInit||o.afterContentInit,i.afterViewChecked=e.afterViewChecked||o.afterViewChecked,i.afterViewInit=e.afterViewInit||o.afterViewInit,i.doCheck=e.doCheck||o.doCheck,i.onDestroy=e.onDestroy||o.onDestroy,i.onInit=e.onInit||o.onInit}var l=o.features;if(l)for(var c=0;c<l.length;c++){var f=l[c];f&&f.ngInherit&&f(e),f===Uf&&(n=!1)}}t=Object.getPrototypeOf(t)}!function d(e){for(var t=0,n=null,r=e.length-1;r>=0;r--){var o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=Qn(o.hostAttrs,n=Qn(n,o.hostAttrs))}}(r)}function Qf(e){return e===We?{}:e===Ke?[]:e}function zf(e,t){var n=e.viewQuery;e.viewQuery=n?function(e,r){t(e,r),n(e,r)}:t}function qf(e,t){var n=e.contentQueries;e.contentQueries=n?function(e,r,o){t(e,r,o),n(e,r,o)}:t}function Zf(e,t){var n=e.hostBindings;e.hostBindings=n?function(e,r){t(e,r),n(e,r)}:t}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var Wf=["providersResolver"],Kf=["template","decls","consts","vars","onPush","ngContentSelectors","styles","encapsulation","schemas"];function Gf(e){var t,n,r,o,i,a=Bf(e.type);i=Ut(e)?a.ɵcmp:a.ɵdir;var u=e;try{for(var s=l(Wf),c=s.next();!c.done;c=s.next())u[p=c.value]=i[p]}catch(e){t={error:e}}finally{try{c&&!c.done&&(n=s.return)&&n.call(s)}finally{if(t)throw t.error}}if(Ut(i))try{for(var f=l(Kf),d=f.next();!d.done;d=f.next()){var p;u[p=d.value]=i[p]}}catch(e){r={error:e}}finally{try{d&&!d.done&&(o=f.return)&&o.call(f)}finally{if(r)throw r.error}}}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var Jf=function(){function e(e,t,n){this.previousValue=e,this.currentValue=t,this.firstChange=n}return e.prototype.isFirstChange=function(){return this.firstChange},e}();
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function Yf(){return $f.ngInherit=!0,$f}function $f(e){e.type.prototype.ngOnChanges&&(e.setInput=Xf,e.onChanges=function t(){return function e(){var t=td(this),n=t&&t.current;if(n){var r=t.previous;if(r===We)t.previous=n;else for(var o in n)r[o]=n[o];t.current=null,this.ngOnChanges(n)}}}())}function Xf(e,t,n,r){var o=td(e)||function i(e,t){return e[ed]=t}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */(e,{previous:We,current:null}),a=o.current||(o.current={}),u=o.previous,s=this.declaredInputs[n],l=u[s];a[s]=new Jf(l&&l.currentValue,t,u===We),e[r]=t}var ed="__ngSimpleChanges__";function td(e){return e[ed]||null}function nd(e,t,n,r,o){if(e=q(e),Array.isArray(e))for(var i=0;i<e.length;i++)nd(e[i],t,n,r,o);else{var a=Gt(),u=Kt(),s=es(e)?e:q(e.provide),l=Yu(e),c=Yt(),f=65535&c.providerIndexes,d=c.directiveStart,p=c.providerIndexes>>16;if(function h(e){return!!e.useClass}(e)||es(e)){var v=(e.useClass||e).prototype.ngOnDestroy;v&&(a.destroyHooks||(a.destroyHooks=[])).push(t.length,v)}if(es(e)||!e.multi){var y=new Rn(l,o,jl),g=od(s,t,o?f:f+p,d);-1==g?(cr(ar(c,u),a,s),t.push(s),c.directiveStart++,c.directiveEnd++,o&&(c.providerIndexes+=65536),n.push(y),u.push(y)):(n[g]=y,u[g]=y)}else{var m=od(s,t,f+p,d),_=od(s,t,f,f+p),w=_>=0&&n[_];o&&!w||!o&&!(m>=0&&n[m])?(cr(ar(c,u),a,s),y=function b(e,t,n,r,o){var i=new Rn(e,n,jl);return i.multi=[],i.index=t,i.componentProviders=0,rd(i,o,r&&!n),i}(o?ad:id,n.length,o,r,l),!o&&w&&(n[_].providerFactory=y),t.push(s),c.directiveStart++,c.directiveEnd++,o&&(c.providerIndexes+=65536),n.push(y),u.push(y)):rd(n[o?_:m],l,!o&&r),!o&&r&&w&&n[_].componentProviders++}}}function rd(e,t,n){e.multi.push(t),n&&e.componentProviders++}function od(e,t,n,r){for(var o=n;o<r;o++)if(t[o]===e)return o;return-1}function id(e,t,n,r){return ud(this.multi,[])}function ad(e,t,n,r){var o,i=this.multi;if(this.providerFactory){var a=this.providerFactory.componentProviders,u=yr(n,n[dt],this.providerFactory.index,r);ud(i,o=u.slice(0,a));for(var s=a;s<u.length;s++)o.push(u[s])}else ud(i,o=[]);return o}function ud(e,t){for(var n=0;n<e.length;n++)t.push((0,e[n])());return t}function sd(e,t){return void 0===t&&(t=[]),function(n){n.providersResolver=function(n,r){return function o(e,t,n){var r=Gt();if(r.firstCreatePass){var o=Ut(e);nd(n,r.data,r.blueprint,o,!0),nd(t,r.data,r.blueprint,o,!1)}}(n,r?r(e):e,t)}}}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var ld=function ld(){},cd=function cd(){};
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
function fd(e){var t=Error("No component factory found for "+B(e)+". Did you add it to @NgModule.entryComponents?");return t[dd]=e,t}var dd="ngComponent",pd=function(){function e(){}return e.prototype.resolveComponentFactory=function(e){throw fd(e)},e}(),hd=function(){function e(){}return e.NULL=new pd,e}(),vd=function(){function e(e,t,n){this._parent=t,this._ngModule=n,this._factories=new Map;for(var r=0;r<e.length;r++){var o=e[r];this._factories.set(o.componentType,o)}}return e.prototype.resolveComponentFactory=function(e){var t=this._factories.get(e);if(!t&&this._parent&&(t=this._parent.resolveComponentFactory(e)),!t)throw fd(e);return new yd(t,this._ngModule)},e}(),yd=function(e){function t(t,n){var r=e.call(this)||this;return r.factory=t,r.ngModule=n,r.selector=t.selector,r.componentType=t.componentType,r.ngContentSelectors=t.ngContentSelectors,r.inputs=t.inputs,r.outputs=t.outputs,r}return o(t,e),t.prototype.create=function(e,t,n,r){return this.factory.create(e,t,n,r||this.ngModule)},t}(cd);
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
function gd(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t]}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var md,_d=function(){function e(e){this.nativeElement=e}return e.__NG_ELEMENT_ID__=function(){return wd(e)},e}(),wd=gd,bd=(new ce("Renderer2Interceptor"),function bd(){});(md=e.RendererStyleFlags2||(e.RendererStyleFlags2={}))[md.Important=1]="Important",md[md.DashCase=2]="DashCase";var Cd,Id=function(){function e(){}return e.__NG_ELEMENT_ID__=function(){return xd()},e}(),xd=gd,Ed=function(){function e(){}return e.ɵprov=T({token:e,providedIn:"root",factory:function(){return null}}),e}(),kd=new(Cd=function Cd(e){this.full=e,this.major=e.split(".")[0],this.minor=e.split(".")[1],this.patch=e.split(".").slice(2).join(".")})("9.0.0"),Od=function(){function e(){}return e.prototype.supports=function(e){return Js(e)},e.prototype.create=function(e){return new Nd(e)},e}(),Pd=function(e,t){return t},Nd=function(){function e(e){this.length=0,this._linkedRecords=null,this._unlinkedRecords=null,this._previousItHead=null,this._itHead=null,this._itTail=null,this._additionsHead=null,this._additionsTail=null,this._movesHead=null,this._movesTail=null,this._removalsHead=null,this._removalsTail=null,this._identityChangesHead=null,this._identityChangesTail=null,this._trackByFn=e||Pd}return e.prototype.forEachItem=function(e){var t;for(t=this._itHead;null!==t;t=t._next)e(t)},e.prototype.forEachOperation=function(e){for(var t=this._itHead,n=this._removalsHead,r=0,o=null;t||n;){var i=!n||t&&t.currentIndex<Ad(n,r,o)?t:n,a=Ad(i,r,o),u=i.currentIndex;if(i===n)r--,n=n._nextRemoved;else if(t=t._next,null==i.previousIndex)r++;else{o||(o=[]);var s=a-r,l=u-r;if(s!=l){for(var c=0;c<s;c++){var f=c<o.length?o[c]:o[c]=0,d=f+c;l<=d&&d<s&&(o[c]=f+1)}o[i.previousIndex]=l-s}}a!==u&&e(i,a,u)}},e.prototype.forEachPreviousItem=function(e){var t;for(t=this._previousItHead;null!==t;t=t._nextPrevious)e(t)},e.prototype.forEachAddedItem=function(e){var t;for(t=this._additionsHead;null!==t;t=t._nextAdded)e(t)},e.prototype.forEachMovedItem=function(e){var t;for(t=this._movesHead;null!==t;t=t._nextMoved)e(t)},e.prototype.forEachRemovedItem=function(e){var t;for(t=this._removalsHead;null!==t;t=t._nextRemoved)e(t)},e.prototype.forEachIdentityChange=function(e){var t;for(t=this._identityChangesHead;null!==t;t=t._nextIdentityChange)e(t)},e.prototype.diff=function(e){if(null==e&&(e=[]),!Js(e))throw new Error("Error trying to diff '"+B(e)+"'. Only arrays and iterables are allowed");return this.check(e)?this:null},e.prototype.onDestroy=function(){},e.prototype.check=function(e){var t=this;this._reset();var n,r,o,i=this._itHead,a=!1;if(Array.isArray(e)){this.length=e.length;for(var u=0;u<this.length;u++)o=this._trackByFn(u,r=e[u]),null!==i&&Ws(i.trackById,o)?(a&&(i=this._verifyReinsertion(i,r,o,u)),Ws(i.item,r)||this._addIdentityChange(i,r)):(i=this._mismatch(i,r,o,u),a=!0),i=i._next}else n=0,function s(e,t){if(Array.isArray(e))for(var n=0;n<e.length;n++)t(e[n]);else for(var r=e[Zs()](),o=void 0;!(o=r.next()).done;)t(o.value)}(e,(function(e){o=t._trackByFn(n,e),null!==i&&Ws(i.trackById,o)?(a&&(i=t._verifyReinsertion(i,e,o,n)),Ws(i.item,e)||t._addIdentityChange(i,e)):(i=t._mismatch(i,e,o,n),a=!0),i=i._next,n++})),this.length=n;return this._truncate(i),this.collection=e,this.isDirty},Object.defineProperty(e.prototype,"isDirty",{get:function(){return null!==this._additionsHead||null!==this._movesHead||null!==this._removalsHead||null!==this._identityChangesHead},enumerable:!0,configurable:!0}),e.prototype._reset=function(){if(this.isDirty){var e=void 0,t=void 0;for(e=this._previousItHead=this._itHead;null!==e;e=e._next)e._nextPrevious=e._next;for(e=this._additionsHead;null!==e;e=e._nextAdded)e.previousIndex=e.currentIndex;for(this._additionsHead=this._additionsTail=null,e=this._movesHead;null!==e;e=t)e.previousIndex=e.currentIndex,t=e._nextMoved;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}},e.prototype._mismatch=function(e,t,n,r){var o;return null===e?o=this._itTail:(o=e._prev,this._remove(e)),null!==(e=null===this._linkedRecords?null:this._linkedRecords.get(n,r))?(Ws(e.item,t)||this._addIdentityChange(e,t),this._moveAfter(e,o,r)):null!==(e=null===this._unlinkedRecords?null:this._unlinkedRecords.get(n,null))?(Ws(e.item,t)||this._addIdentityChange(e,t),this._reinsertAfter(e,o,r)):e=this._addAfter(new Td(t,n),o,r),e},e.prototype._verifyReinsertion=function(e,t,n,r){var o=null===this._unlinkedRecords?null:this._unlinkedRecords.get(n,null);return null!==o?e=this._reinsertAfter(o,e._prev,r):e.currentIndex!=r&&(e.currentIndex=r,this._addToMoves(e,r)),e},e.prototype._truncate=function(e){for(;null!==e;){var t=e._next;this._addToRemovals(this._unlink(e)),e=t}null!==this._unlinkedRecords&&this._unlinkedRecords.clear(),null!==this._additionsTail&&(this._additionsTail._nextAdded=null),null!==this._movesTail&&(this._movesTail._nextMoved=null),null!==this._itTail&&(this._itTail._next=null),null!==this._removalsTail&&(this._removalsTail._nextRemoved=null),null!==this._identityChangesTail&&(this._identityChangesTail._nextIdentityChange=null)},e.prototype._reinsertAfter=function(e,t,n){null!==this._unlinkedRecords&&this._unlinkedRecords.remove(e);var r=e._prevRemoved,o=e._nextRemoved;return null===r?this._removalsHead=o:r._nextRemoved=o,null===o?this._removalsTail=r:o._prevRemoved=r,this._insertAfter(e,t,n),this._addToMoves(e,n),e},e.prototype._moveAfter=function(e,t,n){return this._unlink(e),this._insertAfter(e,t,n),this._addToMoves(e,n),e},e.prototype._addAfter=function(e,t,n){return this._insertAfter(e,t,n),this._additionsTail=null===this._additionsTail?this._additionsHead=e:this._additionsTail._nextAdded=e,e},e.prototype._insertAfter=function(e,t,n){var r=null===t?this._itHead:t._next;return e._next=r,e._prev=t,null===r?this._itTail=e:r._prev=e,null===t?this._itHead=e:t._next=e,null===this._linkedRecords&&(this._linkedRecords=new Dd),this._linkedRecords.put(e),e.currentIndex=n,e},e.prototype._remove=function(e){return this._addToRemovals(this._unlink(e))},e.prototype._unlink=function(e){null!==this._linkedRecords&&this._linkedRecords.remove(e);var t=e._prev,n=e._next;return null===t?this._itHead=n:t._next=n,null===n?this._itTail=t:n._prev=t,e},e.prototype._addToMoves=function(e,t){return e.previousIndex===t?e:(this._movesTail=null===this._movesTail?this._movesHead=e:this._movesTail._nextMoved=e,e)},e.prototype._addToRemovals=function(e){return null===this._unlinkedRecords&&(this._unlinkedRecords=new Dd),this._unlinkedRecords.put(e),e.currentIndex=null,e._nextRemoved=null,null===this._removalsTail?(this._removalsTail=this._removalsHead=e,e._prevRemoved=null):(e._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=e),e},e.prototype._addIdentityChange=function(e,t){return e.item=t,this._identityChangesTail=null===this._identityChangesTail?this._identityChangesHead=e:this._identityChangesTail._nextIdentityChange=e,e},e}(),Td=function Td(e,t){this.item=e,this.trackById=t,this.currentIndex=null,this.previousIndex=null,this._nextPrevious=null,this._prev=null,this._next=null,this._prevDup=null,this._nextDup=null,this._prevRemoved=null,this._nextRemoved=null,this._nextAdded=null,this._nextMoved=null,this._nextIdentityChange=null},jd=function(){function e(){this._head=null,this._tail=null}return e.prototype.add=function(e){null===this._head?(this._head=this._tail=e,e._nextDup=null,e._prevDup=null):(this._tail._nextDup=e,e._prevDup=this._tail,e._nextDup=null,this._tail=e)},e.prototype.get=function(e,t){var n;for(n=this._head;null!==n;n=n._nextDup)if((null===t||t<=n.currentIndex)&&Ws(n.trackById,e))return n;return null},e.prototype.remove=function(e){var t=e._prevDup,n=e._nextDup;return null===t?this._head=n:t._nextDup=n,null===n?this._tail=t:n._prevDup=t,null===this._head},e}(),Dd=function(){function e(){this.map=new Map}return e.prototype.put=function(e){var t=e.trackById,n=this.map.get(t);n||(n=new jd,this.map.set(t,n)),n.add(e)},e.prototype.get=function(e,t){var n=this.map.get(e);return n?n.get(e,t):null},e.prototype.remove=function(e){var t=e.trackById;return this.map.get(t).remove(e)&&this.map.delete(t),e},Object.defineProperty(e.prototype,"isEmpty",{get:function(){return 0===this.map.size},enumerable:!0,configurable:!0}),e.prototype.clear=function(){this.map.clear()},e}();function Ad(e,t,n){var r=e.previousIndex;if(null===r)return r;var o=0;return n&&r<n.length&&(o=n[r]),r+t+o}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var Sd=function(){function e(){}return e.prototype.supports=function(e){return e instanceof Map||Ys(e)},e.prototype.create=function(){return new Rd},e}(),Rd=function(){function e(){this._records=new Map,this._mapHead=null,this._appendAfter=null,this._previousMapHead=null,this._changesHead=null,this._changesTail=null,this._additionsHead=null,this._additionsTail=null,this._removalsHead=null,this._removalsTail=null}return Object.defineProperty(e.prototype,"isDirty",{get:function(){return null!==this._additionsHead||null!==this._changesHead||null!==this._removalsHead},enumerable:!0,configurable:!0}),e.prototype.forEachItem=function(e){var t;for(t=this._mapHead;null!==t;t=t._next)e(t)},e.prototype.forEachPreviousItem=function(e){var t;for(t=this._previousMapHead;null!==t;t=t._nextPrevious)e(t)},e.prototype.forEachChangedItem=function(e){var t;for(t=this._changesHead;null!==t;t=t._nextChanged)e(t)},e.prototype.forEachAddedItem=function(e){var t;for(t=this._additionsHead;null!==t;t=t._nextAdded)e(t)},e.prototype.forEachRemovedItem=function(e){var t;for(t=this._removalsHead;null!==t;t=t._nextRemoved)e(t)},e.prototype.diff=function(e){if(e){if(!(e instanceof Map||Ys(e)))throw new Error("Error trying to diff '"+B(e)+"'. Only maps and objects are allowed")}else e=new Map;return this.check(e)?this:null},e.prototype.onDestroy=function(){},e.prototype.check=function(e){var t=this;this._reset();var n=this._mapHead;if(this._appendAfter=null,this._forEach(e,(function(e,r){if(n&&n.key===r)t._maybeAddToChanges(n,e),t._appendAfter=n,n=n._next;else{var o=t._getOrCreateRecordForKey(r,e);n=t._insertBeforeOrAppend(n,o)}})),n){n._prev&&(n._prev._next=null),this._removalsHead=n;for(var r=n;null!==r;r=r._nextRemoved)r===this._mapHead&&(this._mapHead=null),this._records.delete(r.key),r._nextRemoved=r._next,r.previousValue=r.currentValue,r.currentValue=null,r._prev=null,r._next=null}return this._changesTail&&(this._changesTail._nextChanged=null),this._additionsTail&&(this._additionsTail._nextAdded=null),this.isDirty},e.prototype._insertBeforeOrAppend=function(e,t){if(e){var n=e._prev;return t._next=e,t._prev=n,e._prev=t,n&&(n._next=t),e===this._mapHead&&(this._mapHead=t),this._appendAfter=e,e}return this._appendAfter?(this._appendAfter._next=t,t._prev=this._appendAfter):this._mapHead=t,this._appendAfter=t,null},e.prototype._getOrCreateRecordForKey=function(e,t){if(this._records.has(e)){var n=this._records.get(e);this._maybeAddToChanges(n,t);var r=n._prev,o=n._next;return r&&(r._next=o),o&&(o._prev=r),n._next=null,n._prev=null,n}var i=new Vd(e);return this._records.set(e,i),i.currentValue=t,this._addToAdditions(i),i},e.prototype._reset=function(){if(this.isDirty){var e=void 0;for(this._previousMapHead=this._mapHead,e=this._previousMapHead;null!==e;e=e._next)e._nextPrevious=e._next;for(e=this._changesHead;null!==e;e=e._nextChanged)e.previousValue=e.currentValue;for(e=this._additionsHead;null!=e;e=e._nextAdded)e.previousValue=e.currentValue;this._changesHead=this._changesTail=null,this._additionsHead=this._additionsTail=null,this._removalsHead=null}},e.prototype._maybeAddToChanges=function(e,t){Ws(t,e.currentValue)||(e.previousValue=e.currentValue,e.currentValue=t,this._addToChanges(e))},e.prototype._addToAdditions=function(e){null===this._additionsHead?this._additionsHead=this._additionsTail=e:(this._additionsTail._nextAdded=e,this._additionsTail=e)},e.prototype._addToChanges=function(e){null===this._changesHead?this._changesHead=this._changesTail=e:(this._changesTail._nextChanged=e,this._changesTail=e)},e.prototype._forEach=function(e,t){e instanceof Map?e.forEach(t):Object.keys(e).forEach((function(n){return t(e[n],n)}))},e}(),Vd=function Vd(e){this.key=e,this.previousValue=null,this.currentValue=null,this._nextPrevious=null,this._next=null,this._prev=null,this._nextAdded=null,this._nextRemoved=null,this._nextChanged=null},Fd=function(){function e(e){this.factories=e}return e.create=function(t,n){if(null!=n){var r=n.factories.slice();t=t.concat(r)}return new e(t)},e.extend=function(t){return{provide:e,useFactory:function(n){if(!n)throw new Error("Cannot extend IterableDiffers without a parent injector");return e.create(t,n)},deps:[[e,new x,new C]]}},e.prototype.find=function(e){var t=this.factories.find((function(t){return t.supports(e)}));if(null!=t)return t;throw new Error("Cannot find a differ supporting object '"+e+"' of type '"+function n(e){return e.name||typeof e}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */(e)+"'")},e.ɵprov=T({token:e,providedIn:"root",factory:function(){return new e([new Od])}}),e}(),Md=function(){function e(e){this.factories=e}return e.create=function(t,n){if(n){var r=n.factories.slice();t=t.concat(r)}return new e(t)},e.extend=function(t){return{provide:e,useFactory:function(n){if(!n)throw new Error("Cannot extend KeyValueDiffers without a parent injector");return e.create(t,n)},deps:[[e,new x,new C]]}},e.prototype.find=function(e){var t=this.factories.find((function(t){return t.supports(e)}));if(t)return t;throw new Error("Cannot find a differ supporting object '"+e+"'")},e.ɵprov=T({token:e,providedIn:"root",factory:function(){return new e([new Sd])}}),e}(),Hd=[new Sd],Ld=new Fd([new Od]),Bd=new Md(Hd),Ud=function(){function e(){}return e.__NG_ELEMENT_ID__=function(){return Qd(e,_d)},e}(),Qd=gd,zd=function(){function e(){}return e.__NG_ELEMENT_ID__=function(){return qd(e,_d)},e}(),qd=gd;
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
function Zd(e,t,n,r){var o="ExpressionChangedAfterItHasBeenCheckedError: Expression has changed after it was checked. Previous value: '"+t+"'. Current value: '"+n+"'.";return r&&(o+=" It seems like the view has been created after its parent and its children have been dirty checked. Has it been created in a change detection hook ?"),function i(e,t){var n=new Error(e);return Wd(n,t),n}(o,e)}function Wd(e,t){e[Cr]=t,e[xr]=t.logError.bind(t)}function Kd(e){return new Error("ViewDestroyedError: Attempt to use a destroyed view: "+e)}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function Gd(e,t,n){var r=e.state,o=1792&r;return o===t?(e.state=-1793&r|n,e.initIndex=-1,!0):o===n}function Jd(e,t,n){return(1792&e.state)===t&&e.initIndex<=n&&(e.initIndex=n+1,!0)}function Yd(e,t){return e.nodes[t]}function $d(e,t){return e.nodes[t]}function Xd(e,t){return e.nodes[t]}function ep(e,t){return e.nodes[t]}function tp(e,t){return e.nodes[t]}var np=function np(){},rp={setCurrentNode:void 0,createRootView:void 0,createEmbeddedView:void 0,createComponentView:void 0,createNgModuleRef:void 0,overrideProvider:void 0,overrideComponentView:void 0,clearOverrides:void 0,checkAndUpdateView:void 0,checkNoChangesView:void 0,destroyView:void 0,resolveDep:void 0,createDebugContext:void 0,handleEvent:void 0,updateDirectives:void 0,updateRenderer:void 0,dirtyParentQueries:void 0},op=function(){},ip=new Map;function ap(e){var t=ip.get(e);return t||(t=B(e)+"_"+ip.size,ip.set(e,t)),t}var up=0;function sp(e,t,n,r){return!(!(2&e.state)&&Ws(e.oldValues[t.bindingIndex+n],r))}function lp(e,t,n,r){return!!sp(e,t,n,r)&&(e.oldValues[t.bindingIndex+n]=r,!0)}function cp(e,t,n,r){var o=e.oldValues[t.bindingIndex+n];if(1&e.state||!Ks(o,r)){var i=t.bindings[n].name;throw Zd(rp.createDebugContext(e,t.nodeIndex),i+": "+o,i+": "+r,0!=(1&e.state))}}function fp(e){for(var t=e;t;)2&t.def.flags&&(t.state|=8),t=t.viewContainerParent||t.parent}function dp(e,t){for(var n=e;n&&n!==t;)n.state|=64,n=n.viewContainerParent||n.parent}function pp(e,t,n,r){try{return fp(33554432&e.def.nodes[t].flags?$d(e,t).componentView:e),rp.handleEvent(e,t,n,r)}catch(t){e.root.errorHandler.handleError(t)}}function hp(e){return e.parent?$d(e.parent,e.parentNodeDef.nodeIndex):null}function vp(e){return e.parent?e.parentNodeDef.parent:null}function yp(e,t){switch(201347067&t.flags){case 1:return $d(e,t.nodeIndex).renderElement;case 2:return Yd(e,t.nodeIndex).renderText}}function gp(e){return!!e.parent&&!!(32768&e.parentNodeDef.flags)}function mp(e){return!(!e.parent||32768&e.parentNodeDef.flags)}function _p(e){return 1<<e%32}function wp(e){var t={},n=0,r={};return e&&e.forEach((function(e){var o=c(e,2),i=o[0],a=o[1];"number"==typeof i?(t[i]=a,n|=_p(i)):r[i]=a})),{matchedQueries:t,references:r,matchedQueryIds:n}}function bp(e,t){return e.map((function(e){var n,r,o;return Array.isArray(e)?(o=(n=c(e,2))[0],r=n[1]):(o=0,r=e),r&&("function"==typeof r||"object"==typeof r)&&t&&Object.defineProperty(r,ge,{value:t,configurable:!0}),{flags:o,token:r,tokenKey:ap(r)}}))}function Cp(t,n,r){var o=r.renderParent;return o?0==(1&o.flags)||0==(33554432&o.flags)||o.element.componentRendererType&&o.element.componentRendererType.encapsulation===e.ViewEncapsulation.Native?$d(t,r.renderParent.nodeIndex).renderElement:void 0:n}var Ip=new WeakMap;function xp(e){var t=Ip.get(e);return t||((t=e((function(){return op}))).factory=e,Ip.set(e,t)),t}function Ep(e,t,n,r,o){3===t&&(n=e.renderer.parentNode(yp(e,e.def.lastRenderRootNode))),kp(e,t,0,e.def.nodes.length-1,n,r,o)}function kp(e,t,n,r,o,i,a){for(var u=n;u<=r;u++){var s=e.def.nodes[u];11&s.flags&&Pp(e,s,t,o,i,a),u+=s.childCount}}function Op(e,t,n,r,o,i){for(var a=e;a&&!gp(a);)a=a.parent;for(var u=a.parent,s=vp(a),l=s.nodeIndex+s.childCount,c=s.nodeIndex+1;c<=l;c++){var f=u.def.nodes[c];f.ngContentIndex===t&&Pp(u,f,n,r,o,i),c+=f.childCount}if(!u.parent){var d=e.root.projectableNodes[t];if(d)for(c=0;c<d.length;c++)Np(e,d[c],n,r,o,i)}}function Pp(e,t,n,r,o,i){if(8&t.flags)Op(e,t.ngContent.index,n,r,o,i);else{var a=yp(e,t);if(3===n&&33554432&t.flags&&48&t.bindingFlags?(16&t.bindingFlags&&Np(e,a,n,r,o,i),32&t.bindingFlags&&Np($d(e,t.nodeIndex).componentView,a,n,r,o,i)):Np(e,a,n,r,o,i),16777216&t.flags)for(var u=$d(e,t.nodeIndex).viewContainer._embeddedViews,s=0;s<u.length;s++)Ep(u[s],n,r,o,i);1&t.flags&&!t.element.name&&kp(e,n,t.nodeIndex+1,t.nodeIndex+t.childCount,r,o,i)}}function Np(e,t,n,r,o,i){var a=e.renderer;switch(n){case 1:a.appendChild(r,t);break;case 2:a.insertBefore(r,t,o);break;case 3:a.removeChild(r,t);break;case 0:i.push(t)}}var Tp=/^:([^:]+):(.+)$/;function jp(e){if(":"===e[0]){var t=e.match(Tp);return[t[1],t[2]]}return["",e]}function Dp(e){for(var t=0,n=0;n<e.length;n++)t|=e[n].flags;return t}function Ap(e){return null!=e?e.toString():""}var Sp={},Rp=ap(rs),Vp=ap(fe),Fp=ap(Se);function Mp(e,t,n){void 0===n&&(n=rs.THROW_IF_NOT_FOUND);var r=we(e);try{if(8&t.flags)return t.token;if(2&t.flags&&(n=null),1&t.flags)return e._parent.get(t.token,n);var o=t.tokenKey;switch(o){case Rp:case Vp:case Fp:return e}var i,a=e._def.providersByKey[o];if(a){var u=e._providers[a.index];return void 0===u&&(u=e._providers[a.index]=Hp(e,a)),u===Sp?void 0:u}if((i=A(t.token))&&function s(e,t){var n=t.providedIn;return null!=n&&("any"===n||n===e._def.scope||function r(e,t){return e._def.modules.indexOf(t)>-1}(e,n))}(e,i)){var l=e._providers.length;return e._def.providers[l]=e._def.providersByKey[t.tokenKey]={flags:5120,value:i.factory,deps:[],index:l,token:t.token},e._providers[l]=Sp,e._providers[l]=Hp(e,e._def.providersByKey[t.tokenKey])}return 4&t.flags?n:e._parent.get(t.token,n)}finally{we(r)}}function Hp(e,t){var n;switch(201347067&t.flags){case 512:n=function r(e,t,n){var r=n.length;switch(r){case 0:return new t;case 1:return new t(Mp(e,n[0]));case 2:return new t(Mp(e,n[0]),Mp(e,n[1]));case 3:return new t(Mp(e,n[0]),Mp(e,n[1]),Mp(e,n[2]));default:for(var o=[],i=0;i<r;i++)o[i]=Mp(e,n[i]);return new(t.bind.apply(t,f([void 0],o)))}}(e,t.value,t.deps);break;case 1024:n=function o(e,t,n){var r=n.length;switch(r){case 0:return t();case 1:return t(Mp(e,n[0]));case 2:return t(Mp(e,n[0]),Mp(e,n[1]));case 3:return t(Mp(e,n[0]),Mp(e,n[1]),Mp(e,n[2]));default:for(var o=[],i=0;i<r;i++)o[i]=Mp(e,n[i]);return t.apply(void 0,f(o))}}(e,t.value,t.deps);break;case 2048:n=Mp(e,t.deps[0]);break;case 256:n=t.value}return n===Sp||null===n||"object"!=typeof n||131072&t.flags||"function"!=typeof n.ngOnDestroy||(t.flags|=131072),void 0===n?Sp:n}function Lp(e,t){var n=e.viewContainer._embeddedViews;if((null==t||t>=n.length)&&(t=n.length-1),t<0)return null;var r=n[t];return r.viewContainerParent=null,Be(n,t),rp.dirtyParentQueries(r),Up(r),r}function Bp(e,t,n){var r=t?yp(t,t.def.lastRenderRootNode):e.renderElement,o=n.renderer.parentNode(r),i=n.renderer.nextSibling(r);Ep(n,2,o,i,void 0)}function Up(e){Ep(e,3,null,null,void 0)}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var Qp={};function zp(e){return e.viewDefFactory}var qp=function(e){function t(t,n,r,o,i,a){var u=e.call(this)||this;return u.selector=t,u.componentType=n,u._inputs=o,u._outputs=i,u.ngContentSelectors=a,u.viewDefFactory=r,u}return o(t,e),Object.defineProperty(t.prototype,"inputs",{get:function(){var e=[],t=this._inputs;for(var n in t)e.push({propName:n,templateName:t[n]});return e},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"outputs",{get:function(){var e=[];for(var t in this._outputs)e.push({propName:t,templateName:this._outputs[t]});return e},enumerable:!0,configurable:!0}),t.prototype.create=function(e,t,n,r){if(!r)throw new Error("ngModule should be provided");var o=xp(this.viewDefFactory),i=o.nodes[0].element.componentProvider.nodeIndex,a=rp.createRootView(e,t||[],n,o,r,Qp),u=Xd(a,i).instance;return n&&a.renderer.setAttribute($d(a,0).renderElement,"ng-version",kd.full),new Zp(a,new Jp(a),u)},t}(cd),Zp=function(e){function t(t,n,r){var o=e.call(this)||this;return o._view=t,o._viewRef=n,o._component=r,o._elDef=o._view.def.nodes[0],o.hostView=n,o.changeDetectorRef=n,o.instance=r,o}return o(t,e),Object.defineProperty(t.prototype,"location",{get:function(){return new _d($d(this._view,this._elDef.nodeIndex).renderElement)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"injector",{get:function(){return new eh(this._view,this._elDef)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"componentType",{get:function(){return this._component.constructor},enumerable:!0,configurable:!0}),t.prototype.destroy=function(){this._viewRef.destroy()},t.prototype.onDestroy=function(e){this._viewRef.onDestroy(e)},t}(ld);function Wp(e,t,n){return new Kp(e,t,n)}var Kp=function(){function e(e,t,n){this._view=e,this._elDef=t,this._data=n,this._embeddedViews=[]}return Object.defineProperty(e.prototype,"element",{get:function(){return new _d(this._data.renderElement)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"injector",{get:function(){return new eh(this._view,this._elDef)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"parentInjector",{get:function(){for(var e=this._view,t=this._elDef.parent;!t&&e;)t=vp(e),e=e.parent;return e?new eh(e,t):new eh(this._view,null)},enumerable:!0,configurable:!0}),e.prototype.clear=function(){for(var e=this._embeddedViews.length-1;e>=0;e--){var t=Lp(this._data,e);rp.destroyView(t)}},e.prototype.get=function(e){var t=this._embeddedViews[e];if(t){var n=new Jp(t);return n.attachToViewContainerRef(this),n}return null},Object.defineProperty(e.prototype,"length",{get:function(){return this._embeddedViews.length},enumerable:!0,configurable:!0}),e.prototype.createEmbeddedView=function(e,t,n){var r=e.createEmbeddedView(t||{});return this.insert(r,n),r},e.prototype.createComponent=function(e,t,n,r,o){var i=n||this.parentInjector;o||e instanceof yd||(o=i.get(Se));var a=e.create(i,r,void 0,o);return this.insert(a.hostView,t),a},e.prototype.insert=function(e,t){if(e.destroyed)throw new Error("Cannot insert a destroyed View in a ViewContainer!");var n=e;
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
return function r(e,t,n,o){var i=t.viewContainer._embeddedViews;null==n&&(n=i.length),o.viewContainerParent=e,Le(i,n,o),function a(e,t){var n=hp(t);if(n&&n!==e&&!(16&t.state)){t.state|=16;var r=n.template._projectedViews;r||(r=n.template._projectedViews=[]),r.push(t),function o(e,t){if(!(4&t.flags)){e.nodeFlags|=4,t.flags|=4;for(var n=t.parent;n;)n.childFlags|=4,n=n.parent}}(t.parent.def,t.parentNodeDef)}}(t,o),rp.dirtyParentQueries(o),Bp(t,n>0?i[n-1]:null,o)}(this._view,this._data,t,n._view),n.attachToViewContainerRef(this),e},e.prototype.move=function(e,t){if(e.destroyed)throw new Error("Cannot move a destroyed View in a ViewContainer!");var n=this._embeddedViews.indexOf(e._view);return function r(e,t,n){var r=e.viewContainer._embeddedViews,o=r[t];Be(r,t),null==n&&(n=r.length),Le(r,n,o),rp.dirtyParentQueries(o),Up(o),Bp(e,n>0?r[n-1]:null,o)}(this._data,n,t),e},e.prototype.indexOf=function(e){return this._embeddedViews.indexOf(e._view)},e.prototype.remove=function(e){var t=Lp(this._data,e);t&&rp.destroyView(t)},e.prototype.detach=function(e){var t=Lp(this._data,e);return t?new Jp(t):null},e}();function Gp(e){return new Jp(e)}var Jp=function(){function e(e){this._view=e,this._viewContainerRef=null,this._appRef=null}return Object.defineProperty(e.prototype,"rootNodes",{get:function(){return function e(t){var n=[];return Ep(t,0,void 0,void 0,n),n}(this._view)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"context",{get:function(){return this._view.context},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"destroyed",{get:function(){return 0!=(128&this._view.state)},enumerable:!0,configurable:!0}),e.prototype.markForCheck=function(){fp(this._view)},e.prototype.detach=function(){this._view.state&=-5},e.prototype.detectChanges=function(){var e=this._view.root.rendererFactory;e.begin&&e.begin();try{rp.checkAndUpdateView(this._view)}finally{e.end&&e.end()}},e.prototype.checkNoChanges=function(){rp.checkNoChangesView(this._view)},e.prototype.reattach=function(){this._view.state|=4},e.prototype.onDestroy=function(e){this._view.disposables||(this._view.disposables=[]),this._view.disposables.push(e)},e.prototype.destroy=function(){this._appRef?this._appRef.detachView(this):this._viewContainerRef&&this._viewContainerRef.detach(this._viewContainerRef.indexOf(this)),rp.destroyView(this._view)},e.prototype.detachFromAppRef=function(){this._appRef=null,Up(this._view),rp.dirtyParentQueries(this._view)},e.prototype.attachToAppRef=function(e){if(this._viewContainerRef)throw new Error("This view is already attached to a ViewContainer!");this._appRef=e},e.prototype.attachToViewContainerRef=function(e){if(this._appRef)throw new Error("This view is already attached directly to the ApplicationRef!");this._viewContainerRef=e},e}();function Yp(e,t){return new $p(e,t)}var $p=function(e){function t(t,n){var r=e.call(this)||this;return r._parentView=t,r._def=n,r}return o(t,e),t.prototype.createEmbeddedView=function(e){return new Jp(rp.createEmbeddedView(this._parentView,this._def,this._def.element.template,e))},Object.defineProperty(t.prototype,"elementRef",{get:function(){return new _d($d(this._parentView,this._def.nodeIndex).renderElement)},enumerable:!0,configurable:!0}),t}(Ud);function Xp(e,t){return new eh(e,t)}var eh=function(){function e(e,t){this.view=e,this.elDef=t}return e.prototype.get=function(e,t){return void 0===t&&(t=rs.THROW_IF_NOT_FOUND),rp.resolveDep(this.view,this.elDef,!!this.elDef&&0!=(33554432&this.elDef.flags),{flags:0,token:e,tokenKey:ap(e)},t)},e}();function th(e,t,n,r){return new nh(e,t,n,r)}var nh=function(){function t(e,t,n,r){this._moduleType=e,this._parent=t,this._bootstrapComponents=n,this._def=r,this._destroyListeners=[],this._destroyed=!1,this.injector=this,function o(e){for(var t=e._def,n=e._providers=Ue(t.providers.length),r=0;r<t.providers.length;r++){var o=t.providers[r];4096&o.flags||void 0===n[r]&&(n[r]=Hp(e,o))}}(this)}return t.prototype.get=function(t,n,r){void 0===n&&(n=rs.THROW_IF_NOT_FOUND),void 0===r&&(r=e.InjectFlags.Default);var o=0;return r&e.InjectFlags.SkipSelf?o|=1:r&e.InjectFlags.Self&&(o|=4),Mp(this,{token:t,tokenKey:ap(t),flags:o},n)},Object.defineProperty(t.prototype,"instance",{get:function(){return this.get(this._moduleType)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"componentFactoryResolver",{get:function(){return this.get(hd)},enumerable:!0,configurable:!0}),t.prototype.destroy=function(){if(this._destroyed)throw new Error("The ng module "+B(this.instance.constructor)+" has already been destroyed.");this._destroyed=!0,function e(t,n){for(var r=t._def,o=new Set,i=0;i<r.providers.length;i++)if(131072&r.providers[i].flags){var a=t._providers[i];if(a&&a!==Sp){var u=a.ngOnDestroy;"function"!=typeof u||o.has(a)||(u.apply(a),o.add(a))}}}(this),this._destroyListeners.forEach((function(e){return e()}))},t.prototype.onDestroy=function(e){this._destroyListeners.push(e)},t}(),rh=ap(Id),oh=ap(_d),ih=ap(zd),ah=ap(Ud),uh=ap(yu),sh=ap(rs),lh=ap(fe);
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function ch(e,t,n,r,o,i,a,u,s){var l=wp(n),c=l.matchedQueries,f=l.references,d=l.matchedQueryIds;s||(s=[]),u||(u=[]),i=q(i);var p=bp(a,B(o));return{nodeIndex:-1,parent:null,renderParent:null,bindingIndex:-1,outputIndex:-1,checkIndex:e,flags:t,childFlags:0,directChildFlags:0,childMatchedQueries:0,matchedQueries:c,matchedQueryIds:d,references:f,ngContentIndex:-1,childCount:r,bindings:u,bindingFlags:Dp(u),outputs:s,element:null,provider:{token:o,value:i,deps:p},text:null,query:null,ngContent:null}}function fh(e,t){return vh(e,t)}function dh(e,t){for(var n=e;n.parent&&!gp(n);)n=n.parent;return yh(n.parent,vp(n),!0,t.provider.value,t.provider.deps)}function ph(e,t){var n=yh(e,t.parent,(32768&t.flags)>0,t.provider.value,t.provider.deps);if(t.outputs.length)for(var r=0;r<t.outputs.length;r++){var o=t.outputs[r],i=n[o.propName];if(!Zl(i))throw new Error("@Output "+o.propName+" not initialized in '"+n.constructor.name+"'.");var a=i.subscribe(hh(e,t.parent.nodeIndex,o.eventName));e.disposables[t.outputIndex+r]=a.unsubscribe.bind(a)}return n}function hh(e,t,n){return function(r){return pp(e,t,n,r)}}function vh(e,t){var n=(8192&t.flags)>0,r=t.provider;switch(201347067&t.flags){case 512:return yh(e,t.parent,n,r.value,r.deps);case 1024:return function o(e,t,n,r,i){var a=i.length;switch(a){case 0:return r();case 1:return r(mh(e,t,n,i[0]));case 2:return r(mh(e,t,n,i[0]),mh(e,t,n,i[1]));case 3:return r(mh(e,t,n,i[0]),mh(e,t,n,i[1]),mh(e,t,n,i[2]));default:for(var u=[],s=0;s<a;s++)u.push(mh(e,t,n,i[s]));return r.apply(void 0,f(u))}}(e,t.parent,n,r.value,r.deps);case 2048:return mh(e,t.parent,n,r.deps[0]);case 256:return r.value}}function yh(e,t,n,r,o){var i=o.length;switch(i){case 0:return new r;case 1:return new r(mh(e,t,n,o[0]));case 2:return new r(mh(e,t,n,o[0]),mh(e,t,n,o[1]));case 3:return new r(mh(e,t,n,o[0]),mh(e,t,n,o[1]),mh(e,t,n,o[2]));default:for(var a=[],u=0;u<i;u++)a.push(mh(e,t,n,o[u]));return new(r.bind.apply(r,f([void 0],a)))}}var gh={};function mh(e,t,n,r,o){if(void 0===o&&(o=rs.THROW_IF_NOT_FOUND),8&r.flags)return r.token;var i=e;2&r.flags&&(o=null);var a=r.tokenKey;a===uh&&(n=!(!t||!t.element.componentView)),t&&1&r.flags&&(n=!1,t=t.parent);for(var u=e;u;){if(t)switch(a){case rh:return _h(u,t,n).renderer;case oh:return new _d($d(u,t.nodeIndex).renderElement);case ih:return $d(u,t.nodeIndex).viewContainer;case ah:if(t.element.template)return $d(u,t.nodeIndex).template;break;case uh:return Gp(_h(u,t,n));case sh:case lh:return Xp(u,t);default:var s=(n?t.element.allProviders:t.element.publicProviders)[a];if(s){var l=Xd(u,s.nodeIndex);return l||(l={instance:vh(u,s)},u.nodes[s.nodeIndex]=l),l.instance}}n=gp(u),t=vp(u),u=u.parent,4&r.flags&&(u=null)}var c=i.root.injector.get(r.token,gh);return c!==gh||o===gh?c:i.root.ngModule.injector.get(r.token,o)}function _h(e,t,n){var r;if(n)r=$d(e,t.nodeIndex).componentView;else for(r=e;r.parent&&!gp(r);)r=r.parent;return r}function wh(e,t,n,r,o,i){if(32768&n.flags){var a=$d(e,n.parent.nodeIndex).componentView;2&a.def.flags&&(a.state|=8)}if(t.instance[n.bindings[r].name]=o,524288&n.flags){i=i||{};var u=Gs.unwrap(e.oldValues[n.bindingIndex+r]);i[n.bindings[r].nonMinifiedName]=new Jf(u,o,0!=(2&e.state))}return e.oldValues[n.bindingIndex+r]=o,i}function bh(e,t){if(e.def.nodeFlags&t)for(var n=e.def.nodes,r=0,o=0;o<n.length;o++){var i=n[o],a=i.parent;for(!a&&i.flags&t&&Ih(e,o,i.flags&t,r++),0==(i.childFlags&t)&&(o+=i.childCount);a&&1&a.flags&&o===a.nodeIndex+a.childCount;)a.directChildFlags&t&&(r=Ch(e,a,t,r)),a=a.parent}}function Ch(e,t,n,r){for(var o=t.nodeIndex+1;o<=t.nodeIndex+t.childCount;o++){var i=e.def.nodes[o];i.flags&n&&Ih(e,o,i.flags&n,r++),o+=i.childCount}return r}function Ih(e,t,n,r){var o=Xd(e,t);if(o){var i=o.instance;i&&(rp.setCurrentNode(e,t),1048576&n&&Jd(e,512,r)&&i.ngAfterContentInit(),2097152&n&&i.ngAfterContentChecked(),4194304&n&&Jd(e,768,r)&&i.ngAfterViewInit(),8388608&n&&i.ngAfterViewChecked(),131072&n&&i.ngOnDestroy())}}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var xh=function(e){function t(t){var n=e.call(this)||this;return n.ngModule=t,n}return o(t,e),t.prototype.resolveComponentFactory=function(e){var t=at(e);return new Oh(t,this.ngModule)},t}(hd);function Eh(e){var t=[];for(var n in e)e.hasOwnProperty(n)&&t.push({propName:e[n],templateName:n});return t}var kh=new ce("SCHEDULER_TOKEN",{providedIn:"root",factory:function(){return Yn}}),Oh=function(e){function t(t,n){var r=e.call(this)||this;return r.componentDef=t,r.ngModule=n,r.componentType=t.type,r.selector=function o(e){return e.map(di).join(",")}(t.selectors),r.ngContentSelectors=t.ngContentSelectors?t.ngContentSelectors:[],r.isBoundToModule=!!n,r}return o(t,e),Object.defineProperty(t.prototype,"inputs",{get:function(){return Eh(this.componentDef.inputs)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"outputs",{get:function(){return Eh(this.componentDef.outputs)},enumerable:!0,configurable:!0}),t.prototype.create=function(e,t,n,r){var o,i,a=(r=r||this.ngModule)?function u(e,t){return{get:function(n,r,o){var i=e.get(n,gh,o);return i!==gh||r===gh?i:t.get(n,r,o)}}}(e,r.injector):e,s=a.get(bd,Hn),l=a.get(Ed,null),c=s.createRenderer(null,this.componentDef),f=n?ea(c,n,this.componentDef.encapsulation):Bi(this.componentDef.selectors[0][0]||"div",s.createRenderer(null,this.componentDef),null),d=this.componentDef.onPush?576:528,p="string"==typeof n&&/^#root-ng-internal-isolated-\d+/.test(n),h=Hf(),v=Xi(0,-1,null,1,0,null,null,null,null,null),y=Ui(null,v,h,d,null,null,s,c,l,a),g=n&&f?kd.full:null;hn(y,null);try{var m=Ff(f,this.componentDef,y,s,c,g,null);i=Vo(y[dt],0),t&&(i.projection=t.map((function(e){return Array.from(e)}))),o=Mf(m,this.componentDef,y,h,[Lf]),qi(v,y,null)}finally{gn()}var _=new Nh(this.componentType,o,du(_d,i,y),y,i);return n&&!p||(_.hostView._tViewNode.child=i),_},t}(cd);new xh;var Ph,Nh=function(e){function t(t,n,r,o,i){var a=e.call(this)||this;return a.location=r,a._rootLView=o,a._tNode=i,a.destroyCbs=[],a.instance=n,a.hostView=a.changeDetectorRef=new fu(o),a.hostView._tViewNode=zi(o[dt],null,-1,o),a.componentType=t,a}return o(t,e),Object.defineProperty(t.prototype,"injector",{get:function(){return new _r(this._tNode,this._rootLView)},enumerable:!0,configurable:!0}),t.prototype.destroy=function(){this.destroyCbs&&(this.destroyCbs.forEach((function(e){return e()})),this.destroyCbs=null,!this.hostView.destroyed&&this.hostView.destroy())},t.prototype.onDestroy=function(e){this.destroyCbs&&this.destroyCbs.push(e)},t}(ld),Th=void 0,jh=["en",[["a","p"],["AM","PM"],Th],[["AM","PM"],Th,Th],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],Th,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],Th,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",Th,"{1} 'at' {0}",Th],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤#,##0.00","#E0"],"USD","$","US Dollar",{},function Dh(e){var t=Math.floor(Math.abs(e)),n=e.toString().replace(/^[^.]*\.?/,"").length;return 1===t&&0===n?1:5}],Ah={};
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function Sh(e){var t=function n(e){return e.toLowerCase().replace(/_/g,"-")}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */(e),r=Vh(t);if(r)return r;var o=t.split("-")[0];if(r=Vh(o))return r;if("en"===o)return jh;throw new Error('Missing locale data for the locale "'+e+'".')}function Rh(t){return Sh(t)[e.ɵLocaleDataIndex.PluralCase]}function Vh(e){return e in Ah||(Ah[e]=ee.ng&&ee.ng.common&&ee.ng.common.locales&&ee.ng.common.locales[e]),Ah[e]}(Ph=e.ɵLocaleDataIndex||(e.ɵLocaleDataIndex={}))[Ph.LocaleId=0]="LocaleId",Ph[Ph.DayPeriodsFormat=1]="DayPeriodsFormat",Ph[Ph.DayPeriodsStandalone=2]="DayPeriodsStandalone",Ph[Ph.DaysFormat=3]="DaysFormat",Ph[Ph.DaysStandalone=4]="DaysStandalone",Ph[Ph.MonthsFormat=5]="MonthsFormat",Ph[Ph.MonthsStandalone=6]="MonthsStandalone",Ph[Ph.Eras=7]="Eras",Ph[Ph.FirstDayOfWeek=8]="FirstDayOfWeek",Ph[Ph.WeekendRange=9]="WeekendRange",Ph[Ph.DateFormat=10]="DateFormat",Ph[Ph.TimeFormat=11]="TimeFormat",Ph[Ph.DateTimeFormat=12]="DateTimeFormat",Ph[Ph.NumberSymbols=13]="NumberSymbols",Ph[Ph.NumberFormats=14]="NumberFormats",Ph[Ph.CurrencyCode=15]="CurrencyCode",Ph[Ph.CurrencySymbol=16]="CurrencySymbol",Ph[Ph.CurrencyName=17]="CurrencyName",Ph[Ph.Currencies=18]="Currencies",Ph[Ph.PluralCase=19]="PluralCase",Ph[Ph.ExtraData=20]="ExtraData";var Fh="en-US",Mh="�",Hh=/^\s*(�\d+:?\d*�)\s*,\s*(select|plural)\s*,/,Lh=/�\/?\*(\d+:\d+)�/gi,Bh=/�(\/?[#*!]\d+):?\d*�/gi,Uh=/�(\d+):?\d*�/gi,Qh=/({\s*�\d+:?\d*�\s*,\s*\S{6}\s*,[\s\S]*})/gi,zh=0,qh=/\[(�.+?�?)\]/,Zh=/\[(�.+?�?)\]|(�\/?\*\d+:\d+�)/g,Wh=/({\s*)(VAR_(PLURAL|SELECT)(_\d+)?)(\s*,)/g,Kh=/{([A-Z0-9_]+)}/g,Gh=/�I18N_EXP_(ICU(_\d+)?)�/g,Jh=/\/\*/,Yh=/\d+\:(\d+)/;function $h(e){if(!e)return[];var t,n=0,r=[],o=[],i=/[{}]/g;for(i.lastIndex=0;t=i.exec(e);){var a=t.index;if("}"==t[0]){if(r.pop(),0==r.length){var u=e.substring(n,a);Hh.test(u)?o.push(Xh(u)):o.push(u),n=a+1}}else{if(0==r.length){var s=e.substring(n,a);o.push(s),n=a+1}r.push("{")}}var l=e.substring(n);return o.push(l),o}function Xh(e){for(var t=[],n=[],r=1,o=0,i=$h(e=e.replace(Hh,(function(e,t,n){return r="select"===n?0:1,o=parseInt(t.substr(1),10),""}))),a=0;a<i.length;){var u=i[a++].trim();1===r&&(u=u.replace(/\s*(?:=)?(\w+)\s*/,"$1")),u.length&&t.push(u);var s=$h(i[a++]);t.length>n.length&&n.push(s)}return{type:r,mainBinding:o,cases:t,values:n}}function ev(e){for(var t,n,r="",o=0,i=!1;null!==(t=Lh.exec(e));)i?t[0]===Mh+"/*"+n+Mh&&(o=t.index,i=!1):(r+=e.substring(o,t.index+t[0].length),n=t[1],i=!0);return r+e.substr(o)}function tv(e,t,n,r){void 0===r&&(r=null);for(var o=[null,null],i=e.split(Uh),a=0,u=0;u<i.length;u++){var s=i[u];if(1&u){var l=parseInt(s,10);o.push(-1-l),a|=iv(l)}else""!==s&&o.push(s)}return o.push(t<<2|(n?1:0)),n&&o.push(n,r),o[0]=a,o[1]=o.length-2,o}function nv(e,t){var n;void 0===t&&(t=0),t|=iv(e.mainBinding);for(var r=0;r<e.values.length;r++)for(var o=e.values[r],i=0;i<o.length;i++){var a=o[i];if("string"==typeof a)for(;n=Uh.exec(a);)t|=iv(parseInt(n[1],10));else t=nv(a,t)}return t}var rv=[],ov=-1;function iv(e){return 1<<Math.min(e,31)}var av,uv=[];function sv(e,t,n){var r=Gt();rv[++ov]=e,nc(!0),r.firstCreatePass&&null===r.data[e+Tt]&&function o(e,t,n,r,i){var a=t.blueprint.length-Tt;av=0;var u=Yt(),s=Xt()?u:u&&u.parent,l=s&&s!==e[gt]?s.index-Tt:n,c=0;uv[c]=l;var f=[];n>0&&u!==s&&f.push(u.index<<3|0);for(var d=[],p=[],h=function y(e){return e.replace(Ev," ")}(function v(e,t){if("number"!=typeof t)return ev(e);var n=e.indexOf(":"+t+Mh)+2+t.toString().length,r=e.search(new RegExp(Mh+"\\/\\*\\d+:"+t+Mh));return ev(e.substring(n,r))}(r,i)).split(Bh),g=0;g<h.length;g++){var m=h[g];if(1&g)if("/"===m.charAt(0)){if("#"===m.charAt(1)){var _=parseInt(m.substr(2),10);l=uv[--c],f.push(_<<3|5)}}else _=parseInt(m.substr(1),10),f.push(_<<3|0,l<<17|1),"#"===m.charAt(0)&&(uv[++c]=l=_);else for(var w=$h(m),b=0;b<w.length;b++)if(1&b){var C=a+av++;f.push(wi,"",C,l<<17|1);var I=w[b],x=nv(I);Cv(p,I,C,C);var E=p.length-1;d.push(iv(I.mainBinding),3,-1-I.mainBinding,C<<2|2,E,x,2,C<<2|3,E)}else if(""!==w[b]){var k=w[b],O=k.match(Uh),P=a+av++;f.push(O?"":k,P,l<<17|1),O&&Fe(tv(k,P),d)}}av>0&&function N(e,t,n){if(n>0&&e.firstCreatePass){for(var r=0;r<n;r++)e.blueprint.push(null),e.data.push(null),t.push(null);e.expandoInstructions?e.expandoInstructions.push(n):e.expandoStartIndex+=n}}(t,e,av),t.data[n+Tt]={vars:av,create:f,update:d,icus:p.length?p:null}}(Kt(),r,e,t,n)}function lv(e,t,n,r,o){var i=t.next;r||(r=n),r===n&&t!==n.child?(t.next=n.child,n.child=t):r!==n&&t!==r.next?(t.next=r.next,r.next=t):t.next=null,n!==o[gt]&&(t.parent=n);for(var a=t.next;a;)a.next===t&&(a.next=i),a=a.next;if(1===t.type)return ru(e,o,t),t;$a(e,o,So(t,o),t);var u=o[t.index];return 0!==t.type&&Mt(u)&&$a(e,o,u[St],t),t}function cv(e,t){void 0===t&&(t={});var n=e;if(qh.test(e)){var r={},o=[zh];n=n.replace(Zh,(function(e,t,n){var i=t||n,a=r[i]||[];if(a.length||(i.split("|").forEach((function(e){var t=e.match(Yh),n=t?parseInt(t[1],10):zh,r=Jh.test(e);a.push([n,r,e])})),r[i]=a),!a.length)throw new Error("i18n postprocess: unmatched placeholder - "+i);for(var u=o[o.length-1],s=0,l=0;l<a.length;l++)if(a[l][0]===u){s=l;break}var f=c(a[s],3),d=f[0],p=f[2];return f[1]?o.pop():u!==d&&o.push(d),a.splice(s,1),p}))}return Object.keys(t).length?n=(n=(n=n.replace(Wh,(function(e,n,r,o,i,a){return t.hasOwnProperty(r)?""+n+t[r]+a:e}))).replace(Kh,(function(e,n){return t.hasOwnProperty(n)?t[n]:e}))).replace(Gh,(function(e,n){if(t.hasOwnProperty(n)){var r=t[n];if(!r.length)throw new Error("i18n postprocess: unmatched ICU - "+e+" with key: "+n);return r.shift()}return e})):n}function fv(){var e=Kt();!function t(e,n){for(var r=rv[ov--],o=e.data[r+Tt],i=Yt(),a=pv(r,o.create,e,n),u=r+1;u<=i.index-Tt;){-1===a.indexOf(u)&&hv(e,n,u,!0);var s=Vo(e,u);!s||3!==s.type&&4!==s.type||null===s.localNames||(u+=s.localNames.length>>1),u++}}(Gt(),e),nc(!1)}function dv(e,t,n,r,o,i){var a=Yt();t[n+Tt]=o;var u=Qi(e,t[gt],n,r,i,null);return a&&a.next===u&&(a.next=null),u}function pv(e,t,n,r){for(var o=r[Ct],i=null,a=null,u=[],s=0;s<t.length;s++){var l=t[s];if("string"==typeof l){var c=Fa(l,o),f=t[++s];a=i,i=dv(n,r,f,3,c,null),u.push(f),en()}else if("number"==typeof l)switch(7&l){case 1:var d=l>>>17;a=lv(n,i,d===e?r[gt]:Vo(n,d),a,r);break;case 0:var p=l>>>3;u.push(p),a=i,(i=Vo(n,p))&&$t(i,3===i.type);break;case 5:a=i=Vo(n,l>>>3),$t(i,!1);break;case 4:ha(g=l>>>3,t[++s],t[++s],n,r);break;default:throw new Error('Unable to determine the type of mutate operation for "'+l+'"')}else switch(l){case wi:var h=t[++s],v=t[++s],y=o.createComment(h);a=i,i=dv(n,r,v,5,y,null),u.push(v),Go(y,r),i.activeCaseIndex=null,en();break;case _i:var g,m=t[++s];a=i,i=dv(n,r,g=t[++s],3,o.createElement(m),m),u.push(g);break;default:throw new Error('Unable to determine the type of mutate operation for "'+l+'"')}}return en(),u}function hv(e,t,n,r){var o=Vo(e,n),i=Ao(n,t);i&&eu(t[Ct],i);var a=Fo(t,n);Mt(a)&&0!==o.type&&eu(t[Ct],a[St]),r&&(o.flags|=64)}function vv(e,t,n){sv(e,t,n),fv()}function yv(e,t){!function n(e,t,r,o){for(var i=Yt().index-Tt,a=[],u=0;u<o.length;u+=2)for(var s=o[u],l=o[u+1].split(Qh),c=0;c<l.length;c++){var f=l[c];if(1&c)throw new Error("ICU expressions are not yet supported in attributes");if(""!==f)if(f.match(Uh))t.firstCreatePass&&null===t.data[r+Tt]&&Fe(tv(f,i,s),a);else{var d=Vo(t,i);3===d.type&&ha(i,s,f,t,e);var p=null!==d.inputs&&d.inputs[s];p&&Da(t,e,p,s,f)}}t.firstCreatePass&&null===t.data[r+Tt]&&(t.data[r+Tt]=a)}(Kt(),Gt(),e,t)}var gv=0,mv=0;function _v(e){return el(Kt(),un(),e)&&(gv|=1<<mv),mv++,_v}function wv(e){if(mv){var t=Gt(),n=t.data[e+Tt],r=void 0,o=null;Array.isArray(n)?r=n:(r=n.update,o=n.icus);var i=on()-mv-1,a=Kt();!function e(t,n,r,o,i,a,u){void 0===u&&(u=!1);for(var s=!1,l=0;l<t.length;l++){var c=t[l],f=t[++l];if(u||c&o)for(var d="",p=l+1;p<=l+f;p++){var h=t[p];if("string"==typeof h)d+=h;else if("number"==typeof h)if(h<0)d+=Gn(a[r-h]);else{var v=h>>>2,y=void 0,g=void 0;switch(3&h){case 1:ra(i,a,v,t[++p],d,t[++p]);break;case 0:Aa(a,v,d);break;case 2:if(y=n[t[++p]],null!==(g=Vo(i,v)).activeCaseIndex)for(var m=y.remove[g.activeCaseIndex],_=0;_<m.length;_++){var w=m[_];switch(7&w){case 3:hv(i,a,w>>>3,!1);break;case 6:var b=Vo(i,m[_+1]>>>3).activeCaseIndex;null!==b&&Fe(n[w>>>3].remove[b],m)}}var C=bv(y,d);g.activeCaseIndex=-1!==C?C:null,C>-1&&(pv(-1,y.create[C],i,a),s=!0);break;case 3:y=n[t[++p]],null!==(g=Vo(i,v)).activeCaseIndex&&e(y.update[g.activeCaseIndex],n,r,o,i,a,s)}}}l+=f}}(r,o,i,gv,t,a),gv=0,mv=0}}function bv(e,t){var n=e.cases.indexOf(t);if(-1===n)switch(e.type){case 1:var r=function i(e,t){switch(Rh(t)(e)){case 0:return"zero";case 1:return"one";case 2:return"two";case 3:return"few";case 4:return"many";default:return"other"}}(t,function o(){return kv}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */());-1===(n=e.cases.indexOf(r))&&"other"!==r&&(n=e.cases.indexOf("other"));break;case 0:n=e.cases.indexOf("other")}return n}function Cv(e,t,n,r){for(var o=[],i=[],a=[],u=[],s=[],l=0;l<t.values.length;l++){for(var c=t.values[l],d=[],p=0;p<c.length;p++){var h=c[p];if("string"!=typeof h){var v=d.push(h)-1;c[p]="\x3c!--�"+v+"�--\x3e"}}var y=Iv(c.join(""),n,d,e,r);o.push(y.create),i.push(y.remove),a.push(y.update),u.push(y.vars),s.push(y.childIcus)}e.push({type:t.type,vars:u,childIcus:s,cases:t.cases,create:o,remove:i,update:a}),av+=Math.max.apply(Math,f(u))}function Iv(e,t,n,r,o){var i=new Br(Fn()).getInertBodyElement(e);if(!i)throw new Error("Unable to generate inert body element");var a={vars:0,childIcus:[],create:[],remove:[],update:[]};return function e(t,n,r,o,i,a){if(t){for(var u=[];t;){var s=t.nextSibling,l=a+ ++n.vars;switch(t.nodeType){case Node.ELEMENT_NODE:var c=t,d=c.tagName.toLowerCase();if(no.hasOwnProperty(d)){n.create.push(_i,d,l,r<<17|1);for(var p=c.attributes,h=0;h<p.length;h++){var v=p.item(h),y=v.name.toLowerCase();v.value.match(Uh)?uo.hasOwnProperty(y)&&Fe(ro[y]?tv(v.value,l,v.name,zr):oo[y]?tv(v.value,l,v.name,qr):tv(v.value,l,v.name),n.update):n.create.push(l<<3|4,v.name,v.value)}e(t.firstChild,n,l,o,i,a),n.remove.push(l<<3|3)}else n.vars--;break;case Node.TEXT_NODE:var g=t.textContent||"",m=g.match(Uh);n.create.push(m?"":g,l,r<<17|1),n.remove.push(l<<3|3),m&&Fe(tv(g,l),n.update);break;case Node.COMMENT_NODE:var _=xv.exec(t.textContent||"");if(_){var w=parseInt(_[1],10);n.create.push(wi,"",l,r<<17|1),u.push([b=o[w],l])}else n.vars--;break;default:n.vars--}t=s}for(h=0;h<u.length;h++){var b,C=u[h][1];Cv(i,b=u[h][0],C,a+n.vars);var I=i.length-1;n.vars+=Math.max.apply(Math,f(i[I].vars)),n.childIcus.push(I);var x=nv(b);n.update.push(iv(b.mainBinding),3,-1-b.mainBinding,C<<2|2,I,x,2,C<<2|3,I),n.remove.push(I<<3|6,C<<3|3)}}}((vo(i)||i).firstChild,a,t,n,r,o),a}var xv=/�(\d+)�/,Ev=/\uE500/g,kv=Fh;function Ov(e){Ve(e,"Expected localeId to be defined"),"string"==typeof e&&(kv=e.toLowerCase().replace(/_/g,"-"))}var Pv=new Map;function Nv(e,t,n){if(t&&t!==n)throw new Error("Duplicate module registered for "+e+" - "+B(t)+" vs "+B(t.name))}function Tv(e){if(null!==e.ɵmod.id){var t=e.ɵmod.id;Nv(t,Pv.get(t),e),Pv.set(t,e)}var n=e.ɵmod.imports;n instanceof Function&&(n=n()),n&&n.forEach((function(e){return Tv(e)}))}function jv(e){return Pv.get(e)||et[e]}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var Dv={provide:hd,useClass:xh,deps:[Se]},Av=function(t){function n(e,n){var r=t.call(this)||this;r._parent=n,r._bootstrapComponents=[],r.injector=r,r.destroyCbs=[];var o=ct(e),i=function a(e){return e[ue]||null}(e);return i&&Ov(i),r._bootstrapComponents=tr(o.bootstrap),r._r3Injector=Ku(e,n,[{provide:Se,useValue:r},Dv],B(e)),r.instance=r.get(e),r}return o(n,t),n.prototype.get=function(t,n,r){return void 0===n&&(n=rs.THROW_IF_NOT_FOUND),void 0===r&&(r=e.InjectFlags.Default),t===rs||t===Se||t===fe?this:this._r3Injector.get(t,n,r)},Object.defineProperty(n.prototype,"componentFactoryResolver",{get:function(){return this.get(hd)},enumerable:!0,configurable:!0}),n.prototype.destroy=function(){var e=this._r3Injector;!e.destroyed&&e.destroy(),this.destroyCbs.forEach((function(e){return e()})),this.destroyCbs=null},n.prototype.onDestroy=function(e){this.destroyCbs.push(e)},n}(Se),Sv=function(e){function t(t){var n=e.call(this)||this;return n.moduleType=t,null!==ct(t)&&Tv(t),n}return o(t,e),t.prototype.create=function(e){return new Av(this.moduleType,e)},t}(Re);
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
function Rv(e,t,n){var r=rn()+e,o=Kt();return o[r]===pi?$s(o,r,n?t.call(n):t()):Xs(o,r)}function Vv(e,t,n,r){return qv(Kt(),rn(),e,t,n,r)}function Fv(e,t,n,r,o){return Zv(Kt(),rn(),e,t,n,r,o)}function Mv(e,t,n,r,o,i){return Wv(Kt(),rn(),e,t,n,r,o,i)}function Hv(e,t,n,r,o,i,a){return Kv(Kt(),rn(),e,t,n,r,o,i,a)}function Lv(e,t,n,r,o,i,a,u){var s=rn()+e,l=Kt(),c=rl(l,s,n,r,o,i);return el(l,s+4,a)||c?$s(l,s+5,u?t.call(u,n,r,o,i,a):t(n,r,o,i,a)):Xs(l,s+5)}function Bv(e,t,n,r,o,i,a,u,s){var l=rn()+e,c=Kt(),f=rl(c,l,n,r,o,i);return tl(c,l+4,a,u)||f?$s(c,l+6,s?t.call(s,n,r,o,i,a,u):t(n,r,o,i,a,u)):Xs(c,l+6)}function Uv(e,t,n,r,o,i,a,u,s,l){var c=rn()+e,f=Kt(),d=rl(f,c,n,r,o,i);return nl(f,c+4,a,u,s)||d?$s(f,c+7,l?t.call(l,n,r,o,i,a,u,s):t(n,r,o,i,a,u,s)):Xs(f,c+7)}function Qv(e,t,n,r,o,i,a,u,s,l,c){var f=rn()+e,d=Kt(),p=rl(d,f,n,r,o,i);return rl(d,f+4,a,u,s,l)||p?$s(d,f+8,c?t.call(c,n,r,o,i,a,u,s,l):t(n,r,o,i,a,u,s,l)):Xs(d,f+8)}function zv(e,t,n,r){return Gv(Kt(),rn(),e,t,n,r)}function qv(e,t,n,r,o,i){var a=t+n;return el(e,a,o)?$s(e,a+1,i?r.call(i,o):r(o)):Xs(e,a+1)}function Zv(e,t,n,r,o,i,a){var u=t+n;return tl(e,u,o,i)?$s(e,u+2,a?r.call(a,o,i):r(o,i)):Xs(e,u+2)}function Wv(e,t,n,r,o,i,a,u){var s=t+n;return nl(e,s,o,i,a)?$s(e,s+3,u?r.call(u,o,i,a):r(o,i,a)):Xs(e,s+3)}function Kv(e,t,n,r,o,i,a,u,s){var l=t+n;return rl(e,l,o,i,a,u)?$s(e,l+4,s?r.call(s,o,i,a,u):r(o,i,a,u)):Xs(e,l+4)}function Gv(e,t,n,r,o,i){for(var a=t+n,u=!1,s=0;s<o.length;s++)el(e,a++,o[s])&&(u=!0);return u?$s(e,a,r.apply(i,o)):Xs(e,a)}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function Jv(e,t){var n,r=Gt(),o=e+Tt;r.firstCreatePass?(n=function i(e,t){if(t)for(var n=t.length-1;n>=0;n--){var r=t[n];if(e===r.name)return r}throw new Error("The pipe '"+e+"' could not be found!")}(t,r.pipeRegistry),r.data[o]=n,n.onDestroy&&(r.destroyHooks||(r.destroyHooks=[])).push(o,n.onDestroy)):n=r.data[o];var a=(n.factory||(n.factory=lt(n.type)))();return Nl(r,Kt(),e,a),a}function Yv(e,t,n){var r=Kt(),o=Fo(r,e);return ry(r,ny(r,e)?qv(r,rn(),t,o.transform,n,o):o.transform(n))}function $v(e,t,n,r){var o=Kt(),i=Fo(o,e);return ry(o,ny(o,e)?Zv(o,rn(),t,i.transform,n,r,i):i.transform(n,r))}function Xv(e,t,n,r,o){var i=Kt(),a=Fo(i,e);return ry(i,ny(i,e)?Wv(i,rn(),t,a.transform,n,r,o,a):a.transform(n,r,o))}function ey(e,t,n,r,o,i){var a=Kt(),u=Fo(a,e);return ry(a,ny(a,e)?Kv(a,rn(),t,u.transform,n,r,o,i,u):u.transform(n,r,o,i))}function ty(e,t,n){var r=Kt(),o=Fo(r,e);return ry(r,ny(r,e)?Gv(r,rn(),t,o.transform,n,o):o.transform.apply(o,n))}function ny(e,t){return e[dt].data[t+Tt].pure}function ry(e,t){return Gs.isWrapped(t)&&(t=Gs.unwrap(t),e[on()]=pi),t}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var oy=function(e){function n(t){void 0===t&&(t=!1);var n=e.call(this)||this;return n.__isAsync=t,n}return o(n,e),n.prototype.emit=function(t){e.prototype.next.call(this,t)},n.prototype.subscribe=function(n,r,o){var i,a=function(e){return null},u=function(){return null};n&&"object"==typeof n?(i=this.__isAsync?function(e){setTimeout((function(){return n.next(e)}))}:function(e){n.next(e)},n.error&&(a=this.__isAsync?function(e){setTimeout((function(){return n.error(e)}))}:function(e){n.error(e)}),n.complete&&(u=this.__isAsync?function(){setTimeout((function(){return n.complete()}))}:function(){n.complete()})):(i=this.__isAsync?function(e){setTimeout((function(){return n(e)}))}:function(e){n(e)},r&&(a=this.__isAsync?function(e){setTimeout((function(){return r(e)}))}:function(e){r(e)}),o&&(u=this.__isAsync?function(){setTimeout((function(){return o()}))}:function(){o()}));var s=e.prototype.subscribe.call(this,i,a,u);return n instanceof t.Subscription&&n.add(s),s},n}(t.Subject);
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function iy(){return this._results[Zs()]()}var ay=function(){function e(){this.dirty=!0,this._results=[],this.changes=new oy,this.length=0;var t=Zs(),n=e.prototype;n[t]||(n[t]=iy)}return e.prototype.map=function(e){return this._results.map(e)},e.prototype.filter=function(e){return this._results.filter(e)},e.prototype.find=function(e){return this._results.find(e)},e.prototype.reduce=function(e,t){return this._results.reduce(e,t)},e.prototype.forEach=function(e){this._results.forEach(e)},e.prototype.some=function(e){return this._results.some(e)},e.prototype.toArray=function(){return this._results.slice()},e.prototype.toString=function(){return this._results.toString()},e.prototype.reset=function(e){this._results=Me(e),this.dirty=!1,this.length=this._results.length,this.last=this._results[this.length-1],this.first=this._results[0]},e.prototype.notifyOnChanges=function(){this.changes.emit(this)},e.prototype.setDirty=function(){this.dirty=!0},e.prototype.destroy=function(){this.changes.complete(),this.changes.unsubscribe()},e}(),uy=function(){function e(e){this.queryList=e,this.matches=null}return e.prototype.clone=function(){return new e(this.queryList)},e.prototype.setDirty=function(){this.queryList.setDirty()},e}(),sy=function(){function e(e){void 0===e&&(e=[]),this.queries=e}return e.prototype.createEmbeddedView=function(t){var n=t.queries;if(null!==n){for(var r=null!==t.contentQueries?t.contentQueries[0]:n.length,o=[],i=0;i<r;i++){var a=n.getByIndex(i);o.push(this.queries[a.indexInDeclarationView].clone())}return new e(o)}return null},e.prototype.insertView=function(e){this.dirtyQueriesWithMatches(e)},e.prototype.detachView=function(e){this.dirtyQueriesWithMatches(e)},e.prototype.dirtyQueriesWithMatches=function(e){for(var t=0;t<this.queries.length;t++)null!==Ey(e,t).matches&&this.queries[t].setDirty()},e}(),ly=function ly(e,t,n,r){void 0===r&&(r=null),this.predicate=e,this.descendants=t,this.isStatic=n,this.read=r},cy=function(){function e(e){void 0===e&&(e=[]),this.queries=e}return e.prototype.elementStart=function(e,t){for(var n=0;n<this.queries.length;n++)this.queries[n].elementStart(e,t)},e.prototype.elementEnd=function(e){for(var t=0;t<this.queries.length;t++)this.queries[t].elementEnd(e)},e.prototype.embeddedTView=function(t){for(var n=null,r=0;r<this.length;r++){var o=null!==n?n.length:0,i=this.getByIndex(r).embeddedTView(t,o);i&&(i.indexInDeclarationView=r,null!==n?n.push(i):n=[i])}return null!==n?new e(n):null},e.prototype.template=function(e,t){for(var n=0;n<this.queries.length;n++)this.queries[n].template(e,t)},e.prototype.getByIndex=function(e){return this.queries[e]},Object.defineProperty(e.prototype,"length",{get:function(){return this.queries.length},enumerable:!0,configurable:!0}),e.prototype.track=function(e){this.queries.push(e)},e}(),fy=function(){function e(e,t){void 0===t&&(t=-1),this.metadata=e,this.matches=null,this.indexInDeclarationView=-1,this.crossesNgTemplate=!1,this._appliesToNextNode=!0,this._declarationNodeIndex=t}return e.prototype.elementStart=function(e,t){this.isApplyingToNode(t)&&this.matchTNode(e,t)},e.prototype.elementEnd=function(e){this._declarationNodeIndex===e.index&&(this._appliesToNextNode=!1)},e.prototype.template=function(e,t){this.elementStart(e,t)},e.prototype.embeddedTView=function(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new e(this.metadata)):null},e.prototype.isApplyingToNode=function(e){return this._appliesToNextNode&&!1===this.metadata.descendants?this._declarationNodeIndex===(e.parent?e.parent.index:-1):this._appliesToNextNode},e.prototype.matchTNode=function(e,t){if(Array.isArray(this.metadata.predicate))for(var n=this.metadata.predicate,r=0;r<n.length;r++)this.matchTNodeWithReadOption(e,t,dy(t,n[r]));else{var o=this.metadata.predicate;o===Ud?0===t.type&&this.matchTNodeWithReadOption(e,t,-1):this.matchTNodeWithReadOption(e,t,vr(t,e,o,!1,!1))}},e.prototype.matchTNodeWithReadOption=function(e,t,n){if(null!==n){var r=this.metadata.read;if(null!==r)if(r===_d||r===zd||r===Ud&&0===t.type)this.addMatch(t.index,-2);else{var o=vr(t,e,r,!1,!1);null!==o&&this.addMatch(t.index,o)}else this.addMatch(t.index,n)}},e.prototype.addMatch=function(e,t){null===this.matches?this.matches=[e,t]:this.matches.push(e,t)},e}();
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function dy(e,t){var n=e.localNames;if(null!==n)for(var r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1];return null}function py(e,t,n,r){return-1===n?function o(e,t){return 3===e.type||4===e.type?du(_d,e,t):0===e.type?pu(Ud,_d,e,t):null}(t,e):-2===n?function i(e,t,n){return n===_d?du(_d,t,e):n===Ud?pu(Ud,_d,t,e):n===zd?hu(zd,_d,t,e):void 0}(e,t,r):yr(e,e[dt],n,t)}function hy(e,t,n,r){var o=t[yt].queries[r];if(null===o.matches){for(var i=e.data,a=n.matches,u=[],s=0;s<a.length;s+=2){var l=a[s];u.push(l<0?null:py(t,i[l],a[s+1],n.metadata.read))}o.matches=u}return o.matches}function vy(e){var t=Kt(),n=Gt(),r=cn();fn(r+1);var o=Ey(n,r);if(e.dirty&&Bo(t)===o.metadata.isStatic){if(null===o.matches)e.reset([]);else{var i=o.crossesNgTemplate?function e(t,n,r,o){var i=t.queries.getByIndex(r),a=i.matches;if(null!==a)for(var u=hy(t,n,i,r),s=0;s<a.length;s+=2){var l=a[s];if(l>0)o.push(u[s/2]);else{for(var c=a[s+1],f=n[-l],d=Vt;d<f.length;d++)(v=f[d])[Pt]===v[ht]&&e(v[dt],v,c,o);if(null!==f[At])for(var p=f[At],h=0;h<p.length;h++){var v;e((v=p[h])[dt],v,c,o)}}}return o}(n,t,r,[]):hy(n,t,o,r);e.reset(i),e.notifyOnChanges()}return!0}return!1}function yy(e,t,n){my(Gt(),Kt(),e,t,n,!0)}function gy(e,t,n){my(Gt(),Kt(),e,t,n,!1)}function my(e,t,n,r,o,i){e.firstCreatePass&&(xy(e,new ly(n,r,i,o),-1),i&&(e.staticViewQueries=!0)),Iy(e,t)}function _y(e,t,n,r){by(Gt(),Kt(),t,n,r,!1,Yt(),e)}function wy(e,t,n,r){by(Gt(),Kt(),t,n,r,!0,Yt(),e)}function by(e,t,n,r,o,i,a,u){e.firstCreatePass&&(xy(e,new ly(n,r,i,o),a.index),function s(e,t){var n=e.contentQueries||(e.contentQueries=[]);t!==(e.contentQueries.length?n[n.length-1]:-1)&&n.push(e.queries.length-1,t)}(e,u),i&&(e.staticContentQueries=!0)),Iy(e,t)}function Cy(){return function e(t,n){return t[yt].queries[n].queryList}(Kt(),cn())}function Iy(e,t){var n=new ay;!function r(e,t,n,o){var i=Pa(t);i.push(n),e.firstCreatePass&&Na(e).push(o,i.length-1)}(e,t,n,n.destroy),null===t[yt]&&(t[yt]=new sy),t[yt].queries.push(new uy(n))}function xy(e,t,n){null===e.queries&&(e.queries=new cy),e.queries.track(new fy(t,n))}function Ey(e,t){return e.queries.getByIndex(t)}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function ky(e,t){return pu(Ud,_d,e,t)}function Oy(t){void 0===t&&(t=e.InjectFlags.Default);var n=vu(!0);if(null!=n||t&e.InjectFlags.Optional)return n;throw new Error("No provider for ChangeDetectorRef!")}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var Py={"ɵɵattribute":ol,"ɵɵattributeInterpolate1":hl,"ɵɵattributeInterpolate2":vl,"ɵɵattributeInterpolate3":yl,"ɵɵattributeInterpolate4":gl,"ɵɵattributeInterpolate5":ml,"ɵɵattributeInterpolate6":_l,"ɵɵattributeInterpolate7":wl,"ɵɵattributeInterpolate8":bl,"ɵɵattributeInterpolateV":Cl,"ɵɵdefineComponent":Je,"ɵɵdefineDirective":ot,"ɵɵdefineInjectable":T,"ɵɵdefineInjector":D,"ɵɵdefineNgModule":tt,"ɵɵdefinePipe":it,"ɵɵdirectiveInject":jl,"ɵɵgetFactoryOf":wr,"ɵɵgetInheritedFactory":br,"ɵɵinject":Ie,"ɵɵinjectAttribute":Dl,"ɵɵinvalidFactory":Al,"ɵɵinvalidFactoryDep":xe,"ɵɵinjectPipeChangeDetectorRef":Oy,"ɵɵtemplateRefExtractor":ky,"ɵɵNgOnChangesFeature":Yf,"ɵɵProvidersFeature":sd,"ɵɵCopyDefinitionFeature":Gf,"ɵɵInheritDefinitionFeature":Uf,"ɵɵcontainer":El,"ɵɵnextContext":$l,"ɵɵcontainerRefreshStart":Ol,"ɵɵcontainerRefreshEnd":Pl,"ɵɵnamespaceHTML":In,"ɵɵnamespaceMathML":Cn,"ɵɵnamespaceSVG":bn,"ɵɵenableBindings":Zt,"ɵɵdisableBindings":Wt,"ɵɵelementStart":Vl,"ɵɵelementEnd":Fl,"ɵɵelement":Ml,"ɵɵelementContainerStart":Hl,"ɵɵelementContainerEnd":Ll,"ɵɵelementContainer":Bl,"ɵɵpureFunction0":Rv,"ɵɵpureFunction1":Vv,"ɵɵpureFunction2":Fv,"ɵɵpureFunction3":Mv,"ɵɵpureFunction4":Hv,"ɵɵpureFunction5":Lv,"ɵɵpureFunction6":Bv,"ɵɵpureFunction7":Uv,"ɵɵpureFunction8":Qv,"ɵɵpureFunctionV":zv,"ɵɵgetCurrentView":zl,"ɵɵrestoreView":Jt,"ɵɵlistener":Wl,"ɵɵprojection":rc,"ɵɵupdateSyntheticHostBinding":bf,"ɵɵcomponentHostSyntheticListener":Kl,"ɵɵpipeBind1":Yv,"ɵɵpipeBind2":$v,"ɵɵpipeBind3":Xv,"ɵɵpipeBind4":ey,"ɵɵpipeBindV":ty,"ɵɵprojectionDef":ec,"ɵɵhostProperty":wf,"ɵɵproperty":Sl,"ɵɵpropertyInterpolate":oc,"ɵɵpropertyInterpolate1":ic,"ɵɵpropertyInterpolate2":ac,"ɵɵpropertyInterpolate3":uc,"ɵɵpropertyInterpolate4":sc,"ɵɵpropertyInterpolate5":lc,"ɵɵpropertyInterpolate6":cc,"ɵɵpropertyInterpolate7":fc,"ɵɵpropertyInterpolate8":dc,"ɵɵpropertyInterpolateV":pc,"ɵɵpipe":Jv,"ɵɵqueryRefresh":vy,"ɵɵviewQuery":gy,"ɵɵstaticViewQuery":yy,"ɵɵstaticContentQuery":wy,"ɵɵloadQuery":Cy,"ɵɵcontentQuery":_y,"ɵɵreference":Tl,"ɵɵclassMap":jc,"ɵɵclassMapInterpolate1":tf,"ɵɵclassMapInterpolate2":nf,"ɵɵclassMapInterpolate3":rf,"ɵɵclassMapInterpolate4":of,"ɵɵclassMapInterpolate5":af,"ɵɵclassMapInterpolate6":uf,"ɵɵclassMapInterpolate7":sf,"ɵɵclassMapInterpolate8":lf,"ɵɵclassMapInterpolateV":cf,"ɵɵstyleMap":Nc,"ɵɵstyleProp":Oc,"ɵɵstylePropInterpolate1":ff,"ɵɵstylePropInterpolate2":df,"ɵɵstylePropInterpolate3":pf,"ɵɵstylePropInterpolate4":hf,"ɵɵstylePropInterpolate5":vf,"ɵɵstylePropInterpolate6":yf,"ɵɵstylePropInterpolate7":gf,"ɵɵstylePropInterpolate8":mf,"ɵɵstylePropInterpolateV":_f,"ɵɵstyleSanitizer":kc,"ɵɵclassProp":Pc,"ɵɵselect":gi,"ɵɵadvance":yi,"ɵɵtemplate":kl,"ɵɵtext":zc,"ɵɵtextInterpolate":qc,"ɵɵtextInterpolate1":Zc,"ɵɵtextInterpolate2":Wc,"ɵɵtextInterpolate3":Kc,"ɵɵtextInterpolate4":Gc,"ɵɵtextInterpolate5":Jc,"ɵɵtextInterpolate6":Yc,"ɵɵtextInterpolate7":$c,"ɵɵtextInterpolate8":Xc,"ɵɵtextInterpolateV":ef,"ɵɵembeddedViewStart":Ul,"ɵɵembeddedViewEnd":Ql,"ɵɵi18n":vv,"ɵɵi18nAttributes":yv,"ɵɵi18nExp":_v,"ɵɵi18nStart":sv,"ɵɵi18nEnd":fv,"ɵɵi18nApply":wv,"ɵɵi18nPostprocess":cv,"ɵɵresolveWindow":$n,"ɵɵresolveDocument":Xn,"ɵɵresolveBody":er,"ɵɵsetComponentScope":Ye,"ɵɵsetNgModuleScope":nt,"ɵɵsanitizeHtml":_o,"ɵɵsanitizeStyle":wo,"ɵɵdefaultStyleSanitizer":ko,"ɵɵsanitizeResourceUrl":Co,"ɵɵsanitizeScript":Io,"ɵɵsanitizeUrl":bo,"ɵɵsanitizeUrlOrResourceUrl":Eo},Ny=[],Ty=[],jy=!1;function Dy(){if(!jy){jy=!0;try{for(var e=Ty.length-1;e>=0;e--){var t=Ty[e],n=t.moduleType,r=t.ngModule;r.declarations&&r.declarations.every(Ay)&&(Ty.splice(e,1),Vy(n,r))}}finally{jy=!1}}}function Ay(e){return Array.isArray(e)?e.every(Ay):!!q(e)}function Sy(e,t){void 0===t&&(t={}),Ry(e,t),function n(e,t){Ty.push({moduleType:e,ngModule:t})}(e,t)}function Ry(e,t,n){void 0===n&&(n=!1);var r=Me(t.declarations||Ny),o=null;Object.defineProperty(e,ae,{configurable:!0,get:function(){return null===o&&((o=te().compileNgModule(Py,"ng:///"+e.name+"/ɵmod.js",{type:e,bootstrap:Me(t.bootstrap||Ny).map(q),declarations:r.map(q),imports:Me(t.imports||Ny).map(q).map(Hy),exports:Me(t.exports||Ny).map(q).map(Hy),schemas:t.schemas?Me(t.schemas):null,id:t.id||null})).schemas||(o.schemas=[])),o}});var i=null;Object.defineProperty(e,F,{get:function(){if(null===i){var n={name:e.name,type:e,deps:Tu(e),providers:t.providers||Ny,imports:[(t.imports||Ny).map(q),(t.exports||Ny).map(q)]};i=te().compileInjector(Py,"ng:///"+e.name+"/ɵinj.js",n)}return i},configurable:!1})}function Vy(e,t){var n=Me(t.declarations||Ny),r=My(e);n.forEach((function(t){t.hasOwnProperty(re)?Fy(at(t),r):t.hasOwnProperty(oe)||t.hasOwnProperty(ie)||(t.ngSelectorScope=e)}))}function Fy(e,t){e.directiveDefs=function(){return Array.from(t.compilation.directives).map((function(e){return e.hasOwnProperty(re)?at(e):ut(e)})).filter((function(e){return!!e}))},e.pipeDefs=function(){return Array.from(t.compilation.pipes).map((function(e){return st(e)}))},e.schemas=t.schemas,e.tView=null}function My(e,t){if(void 0===t&&(t=!1),!Ly(e))throw new Error(e.name+" does not have a module def (ɵmod property)");var n=ct(e);if(!t&&null!==n.transitiveCompileScopes)return n.transitiveCompileScopes;var r={schemas:n.schemas||null,compilation:{directives:new Set,pipes:new Set},exported:{directives:new Set,pipes:new Set}};return tr(n.declarations).forEach((function(e){st(e)?r.compilation.pipes.add(e):r.compilation.directives.add(e)})),tr(n.imports).forEach((function(e){var n=e;if(!Ly(n))throw new Error("Importing "+n.name+" which does not have a ɵmod property");var o=My(n,t);o.exported.directives.forEach((function(e){return r.compilation.directives.add(e)})),o.exported.pipes.forEach((function(e){return r.compilation.pipes.add(e)}))})),tr(n.exports).forEach((function(e){var n=e;if(Ly(n)){var o=My(n,t);o.exported.directives.forEach((function(e){r.compilation.directives.add(e),r.exported.directives.add(e)})),o.exported.pipes.forEach((function(e){r.compilation.pipes.add(e),r.exported.pipes.add(e)}))}else st(n)?r.exported.pipes.add(n):r.exported.directives.add(n)})),t||(n.transitiveCompileScopes=r),r}function Hy(e){return function t(e){return void 0!==e.ngModule}(e)?e.ngModule:e}function Ly(e){return!!ct(e)}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function By(t,n){var r=null;!function o(e,t){Bs(t)&&(Hs.set(e,t),Ls.add(e))}(t,n),zy(t,n),Object.defineProperty(t,re,{get:function(){if(null===r){var o=te();if(Bs(n)){var a=["Component '"+t.name+"' is not resolved:"];throw n.templateUrl&&a.push(" - templateUrl: "+n.templateUrl),n.styleUrls&&n.styleUrls.length&&a.push(" - styleUrls: "+JSON.stringify(n.styleUrls)),a.push("Did you run and wait for 'resolveComponentResources()'?"),new Error(a.join("\n"))}var u=n.templateUrl||"ng:///"+t.name+"/template.html",s=i(i({},Zy(t,n)),{typeSourceSpan:o.createParseSourceSpan("Component",t.name,u),template:n.template||"",preserveWhitespaces:n.preserveWhitespaces||!1,styles:n.styles||Ke,animations:n.animations,directives:[],changeDetection:n.changeDetection,pipes:new Map,encapsulation:n.encapsulation||e.ViewEncapsulation.Emulated,interpolation:n.interpolation,viewProviders:n.viewProviders||null});if(s.usesInheritance&&Wy(t),r=o.compileComponent(Py,u,s),Dy(),function l(e){return void 0!==e.ngSelectorScope}(t)){var c=My(t.ngSelectorScope);Fy(r,c)}}return r},configurable:!1})}function Uy(e,t){var n=null;zy(e,t||{}),Object.defineProperty(e,oe,{get:function(){if(null===n){var r=Qy(e,t||{});n=te().compileDirective(Py,r.sourceMapUrl,r.metadata)}return n},configurable:!1})}function Qy(e,t){var n=e&&e.name,r="ng:///"+n+"/ɵdir.js",o=te(),i=Zy(e,t);return i.typeSourceSpan=o.createParseSourceSpan("Directive",n,r),i.usesInheritance&&Wy(e),{metadata:i,sourceMapUrl:r}}function zy(e,t){var n=null;Object.defineProperty(e,se,{get:function(){if(null===n){var r=Qy(e,t),o=te();n=o.compileFactory(Py,"ng:///"+e.name+"/ɵfac.js",i(i({},r.metadata),{injectFn:"directiveInject",target:o.R3FactoryTarget.Directive}))}return n},configurable:!1})}function qy(e){return Object.getPrototypeOf(e.prototype)===Object.prototype}function Zy(e,t){var n,r=Nu(),o=r.ownPropMetadata(e);return{name:e.name,type:e,typeArgumentCount:0,selector:void 0!==t.selector?t.selector:null,deps:Tu(e),host:t.host||We,propMetadata:o,inputs:t.inputs||Ke,outputs:t.outputs||Ke,queries:Ky(e,o,Gy),lifecycle:{usesOnChanges:r.hasLifecycleHook(e,"ngOnChanges")},typeSourceSpan:null,usesInheritance:!qy(e),exportAs:(n=t.exportAs,void 0===n?null:$y(n)),providers:t.providers||null,viewQueries:Ky(e,o,Jy)}}function Wy(e){for(var t=Object.prototype,n=Object.getPrototypeOf(e.prototype).constructor;n&&n!==t;)ut(n)||at(n)||!eg(n)||Uy(n,null),n=Object.getPrototypeOf(n)}function Ky(e,t,n){var r=[],o=function(o){if(t.hasOwnProperty(o)){var i=t[o];i.forEach((function(t){if(n(t)){if(!t.selector)throw new Error("Can't construct a query for the property \""+o+'" of "'+Jn(e)+"\" since the query selector wasn't defined.");if(i.some(Yy))throw new Error("Cannot combine @Input decorators with query decorators");r.push(function a(e,t){return{propertyName:e,predicate:(n=t.selector,"string"==typeof n?$y(n):q(n)),descendants:t.descendants,first:t.first,read:t.read?t.read:null,static:!!t.static};var n}(o,t))}}))}};for(var i in t)o(i);return r}function Gy(e){var t=e.ngMetadataName;return"ContentChild"===t||"ContentChildren"===t}function Jy(e){var t=e.ngMetadataName;return"ViewChild"===t||"ViewChildren"===t}function Yy(e){return"Input"===e.ngMetadataName}function $y(e){return e.split(",").map((function(e){return e.trim()}))}new Map,new Map;var Xy=["ngOnChanges","ngOnInit","ngOnDestroy","ngDoCheck","ngAfterViewInit","ngAfterViewChecked","ngAfterContentInit","ngAfterContentChecked"];function eg(e){var t=Nu();if(Xy.some((function(n){return t.hasLifecycleHook(e,n)})))return!0;var n=t.propMetadata(e);for(var r in n)for(var o=n[r],i=0;i<o.length;i++){var a=o[i],u=a.ngMetadataName;if(Yy(a)||Gy(a)||Jy(a)||"Output"===u||"HostBinding"===u||"HostListener"===u)return!0}return!1}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function tg(e,t){var n=null,r=null;Object.defineProperty(e,se,{get:function(){if(null===r){var n=ng(e,t),o=te();r=o.compileFactory(Py,"ng:///"+n.name+"/ɵfac.js",i(i({},n),{injectFn:"directiveInject",target:o.R3FactoryTarget.Pipe}))}return r},configurable:!1}),Object.defineProperty(e,ie,{get:function(){if(null===n){var r=ng(e,t);n=te().compilePipe(Py,"ng:///"+r.name+"/ɵpipe.js",r)}return n},configurable:!1})}function ng(e,t){return{type:e,typeArgumentCount:0,name:e.name,deps:Tu(e),pipeName:t.name,pure:void 0===t.pure||t.pure}}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var rg=v("Directive",(function(e){return void 0===e&&(e={}),e}),void 0,void 0,(function(e,t){return hg(e,t)})),og=v("Component",(function(t){return void 0===t&&(t={}),i({changeDetection:e.ChangeDetectionStrategy.Default},t)}),rg,void 0,(function(e,t){return pg(e,t)})),ig=v("Pipe",(function(e){return i({pure:!0},e)}),void 0,void 0,(function(e,t){return vg(e,t)})),ag=m("Input",(function(e){return{bindingPropertyName:e}})),ug=m("Output",(function(e){return{bindingPropertyName:e}})),sg=m("HostBinding",(function(e){return{hostPropertyName:e}})),lg=m("HostListener",(function(e,t){return{eventName:e,args:t}})),cg=By,fg=Uy,dg=tg,pg=gd,hg=gd,vg=gd,yg=v("NgModule",(function(e){return e}),void 0,void 0,(function(e,t){return mg(e,t)})),gg=Sy,mg=function _g(e,t){var n=t&&t.imports||[];t&&t.exports&&(n=f(n,[t.exports])),e.ɵinj=D({factory:Mu(e,{useClass:e}),providers:t&&t.providers,imports:n})},wg=new ce("Application Initializer"),bg=function(){function e(e){var t=this;this.appInits=e,this.initialized=!1,this.done=!1,this.donePromise=new Promise((function(e,n){t.resolve=e,t.reject=n}))}return e.prototype.runInitializers=function(){var e=this;if(!this.initialized){var t=[],n=function(){e.done=!0,e.resolve()};if(this.appInits)for(var r=0;r<this.appInits.length;r++){var o=this.appInits[r]();ql(o)&&t.push(o)}Promise.all(t).then((function(){n()})).catch((function(t){e.reject(t)})),0===t.length&&n(),this.initialized=!0}},a([Hu(),u(0,b(wg)),u(0,C()),s("design:paramtypes",[Array])],e)}(),Cg=new ce("AppId");function Ig(){return""+Eg()+Eg()+Eg()}var xg={provide:Cg,useFactory:Ig,deps:[]};function Eg(){return String.fromCharCode(97+Math.floor(25*Math.random()))}var kg,Og=new ce("Platform Initializer"),Pg=new ce("Platform ID"),Ng=new ce("appBootstrapListener"),Tg=new ce("Application Packages Root URL"),jg=function(){function e(){}return e.prototype.log=function(e){console.log(e)},e.prototype.warn=function(e){console.warn(e)},a([Hu()],e)}(),Dg=new ce("LocaleId"),Ag=new ce("DefaultCurrencyCode"),Sg=new ce("Translations"),Rg=new ce("TranslationsFormat");(kg=e.MissingTranslationStrategy||(e.MissingTranslationStrategy={}))[kg.Error=0]="Error",kg[kg.Warning=1]="Warning",kg[kg.Ignore=2]="Ignore";
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
var Vg=!1,Fg=function Fg(e,t){this.ngModuleFactory=e,this.componentFactories=t};function Mg(){throw new Error("Runtime compiler is not loaded")}var Hg=function(e){return new Sv(e)},Lg=Mg,Bg=Mg,Ug=function(e){var t=Hg(e),n=tr(ct(e).declarations).reduce((function(e,t){var n=at(t);return n&&e.push(new Oh(n)),e}),[]);return new Fg(t,n)},Qg=Mg,zg=Mg,qg=function(){function e(){this.compileModuleSync=Lg,this.compileModuleAsync=Bg,this.compileModuleAndAllComponentsSync=Qg,this.compileModuleAndAllComponentsAsync=zg}return e.prototype.clearCache=function(){},e.prototype.clearCacheFor=function(e){},e.prototype.getModuleId=function(e){},a([Hu()],e)}(),Zg=new ce("compilerOptions"),Wg=function Wg(){},Kg=Promise.resolve(0);function Gg(e){"undefined"==typeof Zone?Kg.then((function(){e&&e.apply(null,null)})):Zone.current.scheduleMicroTask("scheduleMicrotask",e)}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
var Jg=function(){function e(e){var t=e.enableLongStackTrace,n=void 0!==t&&t,r=e.shouldCoalesceEventChangeDetection,o=void 0!==r&&r;if(this.hasPendingMacrotasks=!1,this.hasPendingMicrotasks=!1,this.isStable=!0,this.onUnstable=new oy(!1),this.onMicrotaskEmpty=new oy(!1),this.onStable=new oy(!1),this.onError=new oy(!1),"undefined"==typeof Zone)throw new Error("In this configuration Angular requires Zone.js");Zone.assertZonePatched(),this._nesting=0,this._outer=this._inner=Zone.current,Zone.wtfZoneSpec&&(this._inner=this._inner.fork(Zone.wtfZoneSpec)),Zone.TaskTrackingZoneSpec&&(this._inner=this._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(this._inner=this._inner.fork(Zone.longStackTraceZoneSpec)),this.shouldCoalesceEventChangeDetection=o,this.lastRequestAnimationFrameId=-1,this.nativeRequestAnimationFrame=function i(){var e=ee.requestAnimationFrame,t=ee.cancelAnimationFrame;if("undefined"!=typeof Zone&&e&&t){var n=e[Zone.__symbol__("OriginalDelegate")];n&&(e=n);var r=t[Zone.__symbol__("OriginalDelegate")];r&&(t=r)}return{nativeRequestAnimationFrame:e,nativeCancelAnimationFrame:t}}().nativeRequestAnimationFrame,function a(e){var t=!!e.shouldCoalesceEventChangeDetection&&e.nativeRequestAnimationFrame&&function(){!function t(e){-1===e.lastRequestAnimationFrameId&&(e.lastRequestAnimationFrameId=e.nativeRequestAnimationFrame.call(ee,(function(){e.lastRequestAnimationFrameId=-1,em(e),Xg(e)})),em(e))}(e)};e._inner=e._inner.fork({name:"angular",properties:{isAngularZone:!0,maybeDelayChangeDetection:t},onInvokeTask:function(n,r,o,i,a,u){try{return tm(e),n.invokeTask(o,i,a,u)}finally{t&&"eventTask"===i.type&&t(),nm(e)}},onInvoke:function(t,n,r,o,i,a,u){try{return tm(e),t.invoke(r,o,i,a,u)}finally{nm(e)}},onHasTask:function(t,n,r,o){t.hasTask(r,o),n===r&&("microTask"==o.change?(e._hasPendingMicrotasks=o.microTask,em(e),Xg(e)):"macroTask"==o.change&&(e.hasPendingMacrotasks=o.macroTask))},onHandleError:function(t,n,r,o){return t.handleError(r,o),e.runOutsideAngular((function(){return e.onError.emit(o)})),!1}})}(this)}return e.isInAngularZone=function(){return!0===Zone.current.get("isAngularZone")},e.assertInAngularZone=function(){if(!e.isInAngularZone())throw new Error("Expected to be in Angular Zone, but it is not!")},e.assertNotInAngularZone=function(){if(e.isInAngularZone())throw new Error("Expected to not be in Angular Zone, but it is!")},e.prototype.run=function(e,t,n){return this._inner.run(e,t,n)},e.prototype.runTask=function(e,t,n,r){var o=this._inner,i=o.scheduleEventTask("NgZoneEvent: "+r,e,$g,Yg,Yg);try{return o.runTask(i,t,n)}finally{o.cancelTask(i)}},e.prototype.runGuarded=function(e,t,n){return this._inner.runGuarded(e,t,n)},e.prototype.runOutsideAngular=function(e){return this._outer.run(e)},e}();function Yg(){}var $g={};function Xg(e){if(0==e._nesting&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular((function(){return e.onStable.emit(null)}))}finally{e.isStable=!0}}}function em(e){e.hasPendingMicrotasks=!!(e._hasPendingMicrotasks||e.shouldCoalesceEventChangeDetection&&-1!==e.lastRequestAnimationFrameId)}function tm(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function nm(e){e._nesting--,Xg(e)}var rm,om=function(){function e(){this.hasPendingMicrotasks=!1,this.hasPendingMacrotasks=!1,this.isStable=!0,this.onUnstable=new oy,this.onMicrotaskEmpty=new oy,this.onStable=new oy,this.onError=new oy}return e.prototype.run=function(e,t,n){return e.apply(t,n)},e.prototype.runGuarded=function(e,t,n){return e.apply(t,n)},e.prototype.runOutsideAngular=function(e){return e()},e.prototype.runTask=function(e,t,n,r){return e.apply(t,n)},e}(),im=function(){function e(e){var t=this;this._ngZone=e,this._pendingCount=0,this._isZoneStable=!0,this._didWork=!1,this._callbacks=[],this.taskTrackingZone=null,this._watchAngularEvents(),e.run((function(){t.taskTrackingZone="undefined"==typeof Zone?null:Zone.current.get("TaskTrackingZone")}))}return e.prototype._watchAngularEvents=function(){var e=this;this._ngZone.onUnstable.subscribe({next:function(){e._didWork=!0,e._isZoneStable=!1}}),this._ngZone.runOutsideAngular((function(){e._ngZone.onStable.subscribe({next:function(){Jg.assertNotInAngularZone(),Gg((function(){e._isZoneStable=!0,e._runCallbacksIfReady()}))}})}))},e.prototype.increasePendingRequestCount=function(){return this._pendingCount+=1,this._didWork=!0,this._pendingCount},e.prototype.decreasePendingRequestCount=function(){if(this._pendingCount-=1,this._pendingCount<0)throw new Error("pending async requests below zero");return this._runCallbacksIfReady(),this._pendingCount},e.prototype.isStable=function(){return this._isZoneStable&&0===this._pendingCount&&!this._ngZone.hasPendingMacrotasks},e.prototype._runCallbacksIfReady=function(){var e=this;if(this.isStable())Gg((function(){for(;0!==e._callbacks.length;){var t=e._callbacks.pop();clearTimeout(t.timeoutId),t.doneCb(e._didWork)}e._didWork=!1}));else{var t=this.getPendingTasks();this._callbacks=this._callbacks.filter((function(e){return!e.updateCb||!e.updateCb(t)||(clearTimeout(e.timeoutId),!1)})),this._didWork=!0}},e.prototype.getPendingTasks=function(){return this.taskTrackingZone?this.taskTrackingZone.macroTasks.map((function(e){return{source:e.source,creationLocation:e.creationLocation,data:e.data}})):[]},e.prototype.addCallback=function(e,t,n){var r=this,o=-1;t&&t>0&&(o=setTimeout((function(){r._callbacks=r._callbacks.filter((function(e){return e.timeoutId!==o})),e(r._didWork,r.getPendingTasks())}),t)),this._callbacks.push({doneCb:e,timeoutId:o,updateCb:n})},e.prototype.whenStable=function(e,t,n){if(n&&!this.taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/dist/task-tracking.js" loaded?');this.addCallback(e,t,n),this._runCallbacksIfReady()},e.prototype.getPendingRequestCount=function(){return this._pendingCount},e.prototype.findProviders=function(e,t,n){return[]},a([Hu(),s("design:paramtypes",[Jg])],e)}(),am=function(){function e(){this._applications=new Map,um.addToWindow(this)}return e.prototype.registerApplication=function(e,t){this._applications.set(e,t)},e.prototype.unregisterApplication=function(e){this._applications.delete(e)},e.prototype.unregisterAllApplications=function(){this._applications.clear()},e.prototype.getTestability=function(e){return this._applications.get(e)||null},e.prototype.getAllTestabilities=function(){return Array.from(this._applications.values())},e.prototype.getAllRootElements=function(){return Array.from(this._applications.keys())},e.prototype.findTestabilityInTree=function(e,t){return void 0===t&&(t=!0),um.findTestabilityInTree(this,e,t)},a([Hu(),s("design:paramtypes",[])],e)}(),um=new(function(){function e(){}return e.prototype.addToWindow=function(e){},e.prototype.findTestabilityInTree=function(e,t,n){return null},e}()),sm=function lm(){},cm=new ce("AllowMultipleToken"),fm=function fm(e,t){this.name=e,this.token=t};
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function dm(e){if(rm&&!rm.destroyed&&!rm.injector.get(cm,!1))throw new Error("There can be only one platform. Destroy the previous one to create a new one.");sm(),rm=e.get(ym);var t=e.get(Og,null);return t&&t.forEach((function(e){return e()})),rm}function pm(e,t,n){void 0===n&&(n=[]);var r="Platform: "+t,o=new ce(r);return function(t){void 0===t&&(t=[]);var i=vm();if(!i||i.injector.get(cm,!1))if(e)e(n.concat(t).concat({provide:o,useValue:!0}));else{var a=n.concat(t).concat({provide:o,useValue:!0},{provide:Uu,useValue:"platform"});dm(rs.create({providers:a,name:r}))}return hm(o)}}function hm(e){var t=vm();if(!t)throw new Error("No platform exists!");if(!t.injector.get(e,null))throw new Error("A platform with a different configuration has been created. Please destroy it first.");return t}function vm(){return rm&&!rm.destroyed?rm:null}var ym=function(){function e(e){this._injector=e,this._modules=[],this._destroyListeners=[],this._destroyed=!1}return e.prototype.bootstrapModuleFactory=function(e,t){var n=this,r=function o(e,t){return"noop"===e?new om:("zone.js"===e?void 0:e)||new Jg({enableLongStackTrace:Lr(),shouldCoalesceEventChangeDetection:t})}(t?t.ngZone:void 0,t&&t.ngZoneEventCoalescing||!1),i=[{provide:Jg,useValue:r}];return r.run((function(){var t=rs.create({providers:i,parent:n.injector,name:e.moduleType.name}),o=e.create(t),a=o.injector.get(Pr,null);if(!a)throw new Error("No ErrorHandler. Is platform module (BrowserModule) included?");return o.onDestroy((function(){return _m(n._modules,o)})),r.runOutsideAngular((function(){return r.onError.subscribe({next:function(e){a.handleError(e)}})})),function u(e,t,n){try{var r=n();return ql(r)?r.catch((function(n){throw t.runOutsideAngular((function(){return e.handleError(n)})),n})):r}catch(n){throw t.runOutsideAngular((function(){return e.handleError(n)})),n}}(a,r,(function(){var e=o.injector.get(bg);return e.runInitializers(),e.donePromise.then((function(){return Vg&&Ov(o.injector.get(Dg,Fh)||Fh),n._moduleDoBootstrap(o),o}))}))}))},e.prototype.bootstrapModule=function(e,t){var n=this;void 0===t&&(t=[]);var r=gm({},t);return function o(e,t,n){return e.get(Wg).createCompiler([t]).compileModuleAsync(n)}(this.injector,r,e).then((function(e){return n.bootstrapModuleFactory(e,r)}))},e.prototype._moduleDoBootstrap=function(e){var t=e.injector.get(mm);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach((function(e){return t.bootstrap(e)}));else{if(!e.instance.ngDoBootstrap)throw new Error("The module "+B(e.instance.constructor)+' was bootstrapped, but it does not declare "@NgModule.bootstrap" components nor a "ngDoBootstrap" method. Please define one of these.');e.instance.ngDoBootstrap(t)}this._modules.push(e)},e.prototype.onDestroy=function(e){this._destroyListeners.push(e)},Object.defineProperty(e.prototype,"injector",{get:function(){return this._injector},enumerable:!0,configurable:!0}),e.prototype.destroy=function(){if(this._destroyed)throw new Error("The platform has already been destroyed!");this._modules.slice().forEach((function(e){return e.destroy()})),this._destroyListeners.forEach((function(e){return e()})),this._destroyed=!0},Object.defineProperty(e.prototype,"destroyed",{get:function(){return this._destroyed},enumerable:!0,configurable:!0}),a([Hu(),s("design:paramtypes",[rs])],e)}();function gm(e,t){return Array.isArray(t)?t.reduce(gm,e):i(i({},e),t)}var mm=function(){function e(e,r,o,i,a,u){var s=this;this._zone=e,this._console=r,this._injector=o,this._exceptionHandler=i,this._componentFactoryResolver=a,this._initStatus=u,this._bootstrapListeners=[],this._views=[],this._runningTick=!1,this._enforceNoNewChanges=!1,this._stable=!0,this.componentTypes=[],this.components=[],this._enforceNoNewChanges=Lr(),this._zone.onMicrotaskEmpty.subscribe({next:function(){s._zone.run((function(){s.tick()}))}});var l=new t.Observable((function(e){s._stable=s._zone.isStable&&!s._zone.hasPendingMacrotasks&&!s._zone.hasPendingMicrotasks,s._zone.runOutsideAngular((function(){e.next(s._stable),e.complete()}))})),c=new t.Observable((function(e){var t;s._zone.runOutsideAngular((function(){t=s._zone.onStable.subscribe((function(){Jg.assertNotInAngularZone(),Gg((function(){s._stable||s._zone.hasPendingMacrotasks||s._zone.hasPendingMicrotasks||(s._stable=!0,e.next(!0))}))}))}));var n=s._zone.onUnstable.subscribe((function(){Jg.assertInAngularZone(),s._stable&&(s._stable=!1,s._zone.runOutsideAngular((function(){e.next(!1)})))}));return function(){t.unsubscribe(),n.unsubscribe()}}));this.isStable=t.merge(l,c.pipe(n.share()))}return e.prototype.bootstrap=function(e,t){var n,r=this;if(!this._initStatus.done)throw new Error("Cannot bootstrap as there are still asynchronous initializers running. Bootstrap components in the `ngDoBootstrap` method of the root module.");n=e instanceof cd?e:this._componentFactoryResolver.resolveComponentFactory(e),this.componentTypes.push(n.componentType);var o=function i(e){return e instanceof yd}(n)?void 0:this._injector.get(Se),a=n.create(rs.NULL,[],t||n.selector,o);a.onDestroy((function(){r._unloadComponent(a)}));var u=a.injector.get(im,null);return u&&a.injector.get(am).registerApplication(a.location.nativeElement,u),this._loadComponent(a),Lr()&&this._console.log("Angular is running in the development mode. Call enableProdMode() to enable the production mode."),a},e.prototype.tick=function(){var e,t,n,r,o=this;if(this._runningTick)throw new Error("ApplicationRef.tick is called recursively");try{this._runningTick=!0;try{for(var i=l(this._views),a=i.next();!a.done;a=i.next())a.value.detectChanges()}catch(t){e={error:t}}finally{try{a&&!a.done&&(t=i.return)&&t.call(i)}finally{if(e)throw e.error}}if(this._enforceNoNewChanges)try{for(var u=l(this._views),s=u.next();!s.done;s=u.next())s.value.checkNoChanges()}catch(e){n={error:e}}finally{try{s&&!s.done&&(r=u.return)&&r.call(u)}finally{if(n)throw n.error}}}catch(e){this._zone.runOutsideAngular((function(){return o._exceptionHandler.handleError(e)}))}finally{this._runningTick=!1}},e.prototype.attachView=function(e){var t=e;this._views.push(t),t.attachToAppRef(this)},e.prototype.detachView=function(e){var t=e;_m(this._views,t),t.detachFromAppRef()},e.prototype._loadComponent=function(e){this.attachView(e.hostView),this.tick(),this.components.push(e),this._injector.get(Ng,[]).concat(this._bootstrapListeners).forEach((function(t){return t(e)}))},e.prototype._unloadComponent=function(e){this.detachView(e.hostView),_m(this.components,e)},e.prototype.ngOnDestroy=function(){this._views.slice().forEach((function(e){return e.destroy()}))},Object.defineProperty(e.prototype,"viewCount",{get:function(){return this._views.length},enumerable:!0,configurable:!0}),a([Hu(),s("design:paramtypes",[Jg,jg,rs,Pr,hd,bg])],e)}();function _m(e,t){var n=e.indexOf(t);n>-1&&e.splice(n,1)}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
var wm=function wm(){};function bm(e){var t=jv(e);if(!t)throw Im(e);return t}var Cm=bm;function Im(e){return new Error("No module with ID "+e+" loaded")}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var xm=function xm(){},Em={factoryPathPrefix:"",factoryPathSuffix:".ngfactory"},km=function(){function e(e,t){this._compiler=e,this._config=t||Em}return e.prototype.load=function(e){return!Vg&&this._compiler instanceof qg?this.loadFactory(e):this.loadAndCompile(e)},e.prototype.loadAndCompile=function(e){var t=this,n=c(e.split("#"),2),r=n[0],o=n[1];return void 0===o&&(o="default"),System.import(r).then((function(e){return e[o]})).then((function(e){return Om(e,r,o)})).then((function(e){return t._compiler.compileModuleAsync(e)}))},e.prototype.loadFactory=function(e){var t=c(e.split("#"),2),n=t[0],r=t[1],o="NgFactory";return void 0===r&&(r="default",o=""),System.import(this._config.factoryPathPrefix+n+this._config.factoryPathSuffix).then((function(e){return e[r+o]})).then((function(e){return Om(e,n,r)}))},a([Hu(),u(1,C()),s("design:paramtypes",[qg,xm])],e)}();function Om(e,t,n){if(!e)throw new Error("Cannot find '"+n+"' in '"+t+"'");return e}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var Pm=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t}(yu),Nm=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t}(Pm),Tm=function Tm(e,t){this.name=e,this.callback=t},jm=function(){function e(e,t,n){this.listeners=[],this.parent=null,this._debugContext=n,this.nativeNode=e,t&&t instanceof Dm&&t.addChild(this)}return Object.defineProperty(e.prototype,"injector",{get:function(){return this._debugContext.injector},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"componentInstance",{get:function(){return this._debugContext.component},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"context",{get:function(){return this._debugContext.context},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"references",{get:function(){return this._debugContext.references},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"providerTokens",{get:function(){return this._debugContext.providerTokens},enumerable:!0,configurable:!0}),e}(),Dm=function(e){function t(t,n,r){var o=e.call(this,t,n,r)||this;return o.properties={},o.attributes={},o.classes={},o.styles={},o.childNodes=[],o.nativeElement=t,o}return o(t,e),t.prototype.addChild=function(e){e&&(this.childNodes.push(e),e.parent=this)},t.prototype.removeChild=function(e){var t=this.childNodes.indexOf(e);-1!==t&&(e.parent=null,this.childNodes.splice(t,1))},t.prototype.insertChildrenAfter=function(e,t){var n,r=this,o=this.childNodes.indexOf(e);-1!==o&&((n=this.childNodes).splice.apply(n,f([o+1,0],t)),t.forEach((function(t){t.parent&&t.parent.removeChild(t),e.parent=r})))},t.prototype.insertBefore=function(e,t){var n=this.childNodes.indexOf(e);-1===n?this.addChild(t):(t.parent&&t.parent.removeChild(t),t.parent=this,this.childNodes.splice(n,0,t))},t.prototype.query=function(e){return this.queryAll(e)[0]||null},t.prototype.queryAll=function(e){var t=[];return Am(this,e,t),t},t.prototype.queryAllNodes=function(e){var t=[];return Sm(this,e,t),t},Object.defineProperty(t.prototype,"children",{get:function(){return this.childNodes.filter((function(e){return e instanceof t}))},enumerable:!0,configurable:!0}),t.prototype.triggerEventHandler=function(e,t){this.listeners.forEach((function(n){n.name==e&&n.callback(t)}))},t}(jm);function Am(e,t,n){e.childNodes.forEach((function(e){e instanceof Dm&&(t(e)&&n.push(e),Am(e,t,n))}))}function Sm(e,t,n){e instanceof Dm&&e.childNodes.forEach((function(e){t(e)&&n.push(e),e instanceof Dm&&Sm(e,t,n)}))}var Rm=function(){function e(e){this.nativeNode=e}return Object.defineProperty(e.prototype,"parent",{get:function(){var e=this.nativeNode.parentNode;return e?new Vm(e):null},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"injector",{get:function(){return kf(this.nativeNode)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"componentInstance",{get:function(){var e=this.nativeNode;return e&&(Cf(e)||xf(e))},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"context",{get:function(){return Cf(this.nativeNode)||If(this.nativeNode)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"listeners",{get:function(){return Tf(this.nativeNode).filter((function(e){return"dom"===e.type}))},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"references",{get:function(){return function e(t){var n=Pf(t,!1);return null===n?{}:(void 0===n.localRefs&&(n.localRefs=function r(e,t){var n=e[dt].data[t];if(n&&n.localNames){for(var r={},o=n.index+1,i=0;i<n.localNames.length;i+=2)r[n.localNames[i]]=e[o],o++;return r}return null}(n.lView,n.nodeIndex)),n.localRefs||{})}(this.nativeNode)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"providerTokens",{get:function(){return function e(t){var n=Pf(t,!1);if(null===n)return[];for(var r,o=n.lView[dt],i=o.data[n.nodeIndex],a=[],u=i.directiveEnd,s=65535&i.providerIndexes;s<u;s++){var l=o.data[s];void 0!==(r=l).type&&void 0!==r.template&&void 0!==r.declaredInputs&&(l=l.type),a.push(l)}return a}(this.nativeNode)},enumerable:!0,configurable:!0}),e}(),Vm=function(e){function t(t){return e.call(this,t)||this}return o(t,e),Object.defineProperty(t.prototype,"nativeElement",{get:function(){return this.nativeNode.nodeType==Node.ELEMENT_NODE?this.nativeNode:null},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"name",{get:function(){try{var e=Pf(this.nativeNode);return e.lView[dt].data[e.nodeIndex].tagName}catch(e){return this.nativeNode.nodeName}},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"properties",{get:function(){var e=Pf(this.nativeNode,!1);if(null==e)return{};var t=e.lView,n=t[dt].data,r=n[e.nodeIndex],o={};return function i(e,t){if(e)for(var n=Object.getPrototypeOf(e),r=Node.prototype;null!==n&&n!==r;){var o=Object.getOwnPropertyDescriptors(n);for(var i in o)if(!i.startsWith("__")&&!i.startsWith("on")){var a=e[i];Fm(a)&&(t[i]=a)}n=Object.getPrototypeOf(n)}}(this.nativeElement,o),function a(e,t,n,r){var o=t.propertyBindings;if(null!==o)for(var i=0;i<o.length;i++){var a=o[i],u=r[a].split("�"),s=u[0];if(u.length>1){for(var l=u[1],c=1;c<u.length-1;c++)l+=Gn(n[a+c-1])+u[c+1];e[s]=l}else e[s]=n[a]}}(o,r,t,n),o},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"attributes",{get:function(){var e={},t=this.nativeElement;if(!t)return e;var n=Pf(t,!1);if(null==n)return{};var r=n.lView[dt].data[n.nodeIndex].attrs,o=[];if(r)for(var i=0;i<r.length;){var a=r[i];if("string"!=typeof a)break;e[a]=r[i+1],o.push(a.toLowerCase()),i+=2}var u=t.attributes;for(i=0;i<u.length;i++){var s=u[i],l=s.name.toLowerCase();-1===o.indexOf(l)&&(e[l]=s.value)}return e},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"styles",{get:function(){return this.nativeElement&&this.nativeElement.style?this.nativeElement.style:{}},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"classes",{get:function(){var e={},t=this.nativeElement.className;return(t&&"string"!=typeof t?t.baseVal.split(" "):t.split(" ")).forEach((function(t){return e[t]=!0})),e},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"childNodes",{get:function(){for(var e=this.nativeNode.childNodes,t=[],n=0;n<e.length;n++)t.push(Zm(e[n]));return t},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"children",{get:function(){var e=this.nativeElement;if(!e)return[];for(var t=e.children,n=[],r=0;r<t.length;r++)n.push(Zm(t[r]));return n},enumerable:!0,configurable:!0}),t.prototype.query=function(e){return this.queryAll(e)[0]||null},t.prototype.queryAll=function(e){var t=[];return Mm(this,e,t,!0),t},t.prototype.queryAllNodes=function(e){var t=[];return Mm(this,e,t,!1),t},t.prototype.triggerEventHandler=function(e,t){var n=this.nativeNode,r=[];this.listeners.forEach((function(o){if(o.name===e){var i=o.callback;i.call(n,t),r.push(i)}})),"function"==typeof n.eventListeners&&n.eventListeners(e).forEach((function(e){if(-1!==e.toString().indexOf("__ngUnwrap__")){var o=e("__ngUnwrap__");return-1===r.indexOf(o)&&o.call(n,t)}}))},t}(Rm);function Fm(e){return"string"==typeof e||"boolean"==typeof e||"number"==typeof e||null===e}function Mm(e,t,n,r){var o=Pf(e.nativeNode,!1);null!==o?Hm(o.lView[dt].data[o.nodeIndex],o.lView,t,n,r,e.nativeNode):Um(e.nativeNode,t,n,r)}function Hm(e,t,n,r,o,i){var a,u,s=Ro(e,t);if(3===e.type||4===e.type){Bm(s,n,r,o,i),Lt(e)?(d=Mo(e.index,t))&&d[dt].firstChild&&Hm(d[dt].firstChild,d,n,r,o,i):(e.child&&Hm(e.child,t,n,r,o,i),s&&Um(s,n,r,o));var c=t[e.index];Mt(c)&&Lm(c,n,r,o,i)}else if(0===e.type){var f=t[e.index];Bm(f[St],n,r,o,i),Lm(f,n,r,o,i)}else if(1===e.type){var d,p=(d=t[Ot])[gt].projection[e.projection];if(Array.isArray(p))try{for(var h=l(p),v=h.next();!v.done;v=h.next())Bm(v.value,n,r,o,i)}catch(e){a={error:e}}finally{try{v&&!v.done&&(u=h.return)&&u.call(h)}finally{if(a)throw a.error}}else if(p){var y,g=d[ht];Hm(y=g[dt].data[p.index],g,n,r,o,i)}}else e.child&&Hm(e.child,t,n,r,o,i);i!==s&&(y=4&e.flags?e.projectionNext:e.next)&&Hm(y,t,n,r,o,i)}function Lm(e,t,n,r,o){for(var i=Vt;i<e.length;i++){var a=e[i];Hm(a[dt].node,a,t,n,r,o)}}function Bm(e,t,n,r,o){if(o!==e){var i=Wm(e);if(!i)return;r&&i instanceof Vm&&t(i)&&-1===n.indexOf(i)?n.push(i):!r&&t(i)&&-1===n.indexOf(i)&&n.push(i)}}function Um(e,t,n,r){for(var o=e.childNodes,i=o.length,a=0;a<i;a++){var u=o[a],s=Wm(u);s&&(r&&s instanceof Vm&&t(s)&&-1===n.indexOf(s)?n.push(s):!r&&t(s)&&-1===n.indexOf(s)&&n.push(s),Um(u,t,n,r))}}var Qm=new Map;function zm(e){return Qm.get(e)||null}var qm="__ng_debug__";function Zm(e){return e instanceof Node?(e.hasOwnProperty(qm)||(e[qm]=e.nodeType==Node.ELEMENT_NODE?new Vm(e):new Rm(e)),e[qm]):null}var Wm=zm;function Km(e){return zm(e)}var Gm=Km;function Jm(e){Qm.set(e.nativeNode,e)}var Ym=jm,$m=Dm,Xm=pm(null,"core",[{provide:Pg,useValue:"unknown"},{provide:ym,deps:[rs]},{provide:am,deps:[]},{provide:jg,deps:[]}]);
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
function e_(){return Ld}function t_(){return Bd}function n_(e){return e=e||function t(){return Vg&&"undefined"!=typeof $localize&&$localize.locale||Fh}(),Vg&&Ov(e),e}var r_=[{provide:mm,useClass:mm,deps:[Jg,jg,rs,Pr,hd,bg]},{provide:kh,deps:[Jg],useFactory:o_},{provide:bg,useClass:bg,deps:[[new C,wg]]},{provide:qg,useClass:qg,deps:[]},xg,{provide:Fd,useFactory:e_,deps:[]},{provide:Md,useFactory:t_,deps:[]},{provide:Dg,useFactory:n_,deps:[[new b(Dg),new C,new x]]},{provide:Ag,useValue:"USD"}];function o_(e){var t=[];return e.onStable.subscribe((function(){for(;t.length;)t.pop()()})),function(e){t.push(e)}}var i_,a_=a([yg({providers:r_}),s("design:paramtypes",[mm])],(function a_(e){}));
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function u_(t,n,r){var o,i=r.element,a=t.root.selectorOrNode,u=t.renderer;if(t.parent||!a){o=i.name?u.createElement(i.name,i.ns):u.createComment("");var s=Cp(t,n,r);s&&u.appendChild(s,o)}else o=u.selectRootElement(a,!!i.componentRendererType&&i.componentRendererType.encapsulation===e.ViewEncapsulation.ShadowDom);if(i.attrs)for(var l=0;l<i.attrs.length;l++){var f=c(i.attrs[l],3);u.setAttribute(o,f[1],f[2],f[0])}return o}function s_(e,t,n,r){for(var o=0;o<n.outputs.length;o++){var i=n.outputs[o],a=l_(e,n.nodeIndex,(f=i.eventName,(c=i.target)?c+":"+f:f)),u=i.target,s=e;"component"===i.target&&(u=null,s=t);var l=s.renderer.listen(u||r,i.eventName,a);e.disposables[n.outputIndex+o]=l}var c,f}function l_(e,t,n){return function(r){return pp(e,t,n,r)}}function c_(t,n,r,o){if(!lp(t,n,r,o))return!1;var i=n.bindings[r],a=$d(t,n.nodeIndex),u=a.renderElement,s=i.name;switch(15&i.flags){case 1:!function l(e,t,n,r,o,i){var a=t.securityContext,u=a?e.root.sanitizer.sanitize(a,i):i;u=null!=u?u.toString():null;var s=e.renderer;null!=i?s.setAttribute(n,o,u,r):s.removeAttribute(n,o,r)}(t,i,u,i.ns,s,o);break;case 2:!function c(e,t,n,r){var o=e.renderer;r?o.addClass(t,n):o.removeClass(t,n)}(t,u,s,o);break;case 4:!function f(t,n,r,o,i){var a=t.root.sanitizer.sanitize(e.SecurityContext.STYLE,i);if(null!=a){a=a.toString();var u=n.suffix;null!=u&&(a+=u)}else a=null;var s=t.renderer;null!=a?s.setStyle(r,o,a):s.removeStyle(r,o)}(t,i,u,s,o);break;case 8:!function d(e,t,n,r,o){var i=t.securityContext,a=i?e.root.sanitizer.sanitize(i,o):o;e.renderer.setProperty(n,r,a)}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */(33554432&n.flags&&32&i.flags?a.componentView:t,i,u,s,o)}return!0}function f_(e){for(var t=e.def.nodeMatchedQueries;e.parent&&mp(e);){var n=e.parentNodeDef;e=e.parent;for(var r=n.nodeIndex+n.childCount,o=0;o<=r;o++)67108864&(i=e.def.nodes[o]).flags&&536870912&i.flags&&(i.query.filterId&t)===i.query.filterId&&tp(e,o).setDirty(),!(1&i.flags&&o+i.childCount<n.nodeIndex)&&67108864&i.childFlags&&536870912&i.childFlags||(o+=i.childCount)}if(134217728&e.def.nodeFlags)for(o=0;o<e.def.nodes.length;o++){var i;134217728&(i=e.def.nodes[o]).flags&&536870912&i.flags&&tp(e,o).setDirty(),o+=i.childCount}}function d_(e,t){var n=tp(e,t.nodeIndex);if(n.dirty){var r,o=void 0;if(67108864&t.flags){var i=t.parent.parent;o=p_(e,i.nodeIndex,i.nodeIndex+i.childCount,t.query,[]),r=Xd(e,t.parent.nodeIndex).instance}else 134217728&t.flags&&(o=p_(e,0,e.def.nodes.length-1,t.query,[]),r=e.component);n.reset(o);for(var a=t.query.bindings,u=!1,s=0;s<a.length;s++){var l=a[s],c=void 0;switch(l.bindingType){case 0:c=n.first;break;case 1:c=n,u=!0}r[l.propName]=c}u&&n.notifyOnChanges()}}function p_(e,t,n,r,o){for(var i=t;i<=n;i++){var a=e.def.nodes[i],u=a.matchedQueries[r.id];if(null!=u&&o.push(h_(e,a,u)),1&a.flags&&a.element.template&&(a.element.template.nodeMatchedQueries&r.filterId)===r.filterId){var s=$d(e,i);if((a.childMatchedQueries&r.filterId)===r.filterId&&(p_(e,i+1,i+a.childCount,r,o),i+=a.childCount),16777216&a.flags)for(var l=s.viewContainer._embeddedViews,c=0;c<l.length;c++){var f=l[c],d=hp(f);d&&d===s&&p_(f,0,f.def.nodes.length-1,r,o)}var p=s.template._projectedViews;if(p)for(c=0;c<p.length;c++){var h=p[c];p_(h,0,h.def.nodes.length-1,r,o)}}(a.childMatchedQueries&r.filterId)!==r.filterId&&(i+=a.childCount)}return o}function h_(e,t,n){if(null!=n)switch(n){case 1:return $d(e,t.nodeIndex).renderElement;case 0:return new _d($d(e,t.nodeIndex).renderElement);case 2:return $d(e,t.nodeIndex).template;case 3:return $d(e,t.nodeIndex).viewContainer;case 4:return Xd(e,t.nodeIndex).instance}}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function v_(e,t,n){var r=Cp(e,t,n);r&&Op(e,n.ngContent.index,1,r,null,void 0)}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function y_(e,t,n){for(var r=[],o=0;o<n.length;o++){var i=n[o];r.push({flags:8,name:i,ns:null,nonMinifiedName:i,securityContext:null,suffix:null})}return{nodeIndex:-1,parent:null,renderParent:null,bindingIndex:-1,outputIndex:-1,checkIndex:t,flags:e,childFlags:0,directChildFlags:0,childMatchedQueries:0,matchedQueries:{},matchedQueryIds:0,references:{},ngContentIndex:-1,childCount:0,bindings:r,bindingFlags:Dp(r),outputs:[],element:null,provider:null,text:null,query:null,ngContent:null}}function g_(e,t,n){var r,o=e.renderer;r=o.createText(n.text.prefix);var i=Cp(e,t,n);return i&&o.appendChild(i,r),{renderText:r}}function m_(e,t){return(null!=e?e.toString():"")+t.suffix}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function __(e){return 0!=(1&e.flags)&&null===e.element.name}function w_(e,t,n){var r=t.element&&t.element.template;if(r){if(!r.lastRenderRootNode)throw new Error("Illegal State: Embedded templates without nodes are not allowed!");if(r.lastRenderRootNode&&16777216&r.lastRenderRootNode.flags)throw new Error("Illegal State: Last root node of a template can't have embedded views, at index "+t.nodeIndex+"!")}if(20224&t.flags&&0==(1&(e?e.flags:0)))throw new Error("Illegal State: StaticProvider/Directive nodes need to be children of elements or anchors, at index "+t.nodeIndex+"!");if(t.query){if(67108864&t.flags&&(!e||0==(16384&e.flags)))throw new Error("Illegal State: Content Query nodes need to be children of directives, at index "+t.nodeIndex+"!");if(134217728&t.flags&&e)throw new Error("Illegal State: View Query nodes have to be top level nodes, at index "+t.nodeIndex+"!")}if(t.childCount){var o=e?e.nodeIndex+e.childCount:n-1;if(t.nodeIndex<=o&&t.nodeIndex+t.childCount>o)throw new Error("Illegal State: childCount of node leads outside of parent, at index "+t.nodeIndex+"!")}}function b_(e,t,n,r){var o=x_(e.root,e.renderer,e,t,n);return E_(o,e.component,r),k_(o),o}function C_(e,t,n){var r=x_(e,e.renderer,null,null,t);return E_(r,n,n),k_(r),r}function I_(e,t,n,r){var o,i=t.element.componentRendererType;return o=i?e.root.rendererFactory.createRenderer(r,i):e.root.renderer,x_(e.root,o,e,t.element.componentProvider,n)}function x_(e,t,n,r,o){var i=new Array(o.nodes.length),a=o.outputCount?new Array(o.outputCount):null;return{def:o,parent:n,viewContainerParent:null,parentNodeDef:r,context:null,component:null,nodes:i,state:13,root:e,renderer:t,oldValues:new Array(o.bindingCount),disposables:a,initIndex:-1}}function E_(e,t,n){e.component=t,e.context=n}function k_(e){var t;gp(e)&&(t=$d(e.parent,e.parentNodeDef.parent.nodeIndex).renderElement);for(var n=e.def,r=e.nodes,o=0;o<n.nodes.length;o++){var i=n.nodes[o];rp.setCurrentNode(e,o);var a=void 0;switch(201347067&i.flags){case 1:var u=u_(e,t,i),s=void 0;if(33554432&i.flags){var l=xp(i.element.componentView);s=rp.createComponentView(e,i,l,u)}s_(e,s,i,u),a={renderElement:u,componentView:s,viewContainer:null,template:i.element.template?Yp(e,i):void 0},16777216&i.flags&&(a.viewContainer=Wp(e,i,a));break;case 2:a=g_(e,t,i);break;case 512:case 1024:case 2048:case 256:(a=r[o])||4096&i.flags||(a={instance:fh(e,i)});break;case 16:a={instance:dh(e,i)};break;case 16384:(a=r[o])||(a={instance:ph(e,i)}),32768&i.flags&&E_($d(e,i.parent.nodeIndex).componentView,a.instance,a.instance);break;case 32:case 64:case 128:a={value:void 0};break;case 67108864:case 134217728:a=new ay;break;case 8:v_(e,t,i),a=void 0}r[o]=a}S_(e,i_.CreateViewNodes),M_(e,201326592,268435456,0)}function O_(e){T_(e),rp.updateDirectives(e,1),R_(e,i_.CheckNoChanges),rp.updateRenderer(e,1),S_(e,i_.CheckNoChanges),e.state&=-97}function P_(e){1&e.state?(e.state&=-2,e.state|=2):e.state&=-3,Gd(e,0,256),T_(e),rp.updateDirectives(e,0),R_(e,i_.CheckAndUpdate),M_(e,67108864,536870912,0);var t=Gd(e,256,512);bh(e,2097152|(t?1048576:0)),rp.updateRenderer(e,0),S_(e,i_.CheckAndUpdate),M_(e,134217728,536870912,0),bh(e,8388608|((t=Gd(e,512,768))?4194304:0)),2&e.def.flags&&(e.state&=-9),e.state&=-97,Gd(e,768,1024)}function N_(e,t,n,r,o,i,a,u,s,l,c,d,p){return 0===n?function h(e,t,n,r,o,i,a,u,s,l,c,f){switch(201347067&t.flags){case 1:return function d(e,t,n,r,o,i,a,u,s,l,c,f){var d=t.bindings.length,p=!1;return d>0&&c_(e,t,0,n)&&(p=!0),d>1&&c_(e,t,1,r)&&(p=!0),d>2&&c_(e,t,2,o)&&(p=!0),d>3&&c_(e,t,3,i)&&(p=!0),d>4&&c_(e,t,4,a)&&(p=!0),d>5&&c_(e,t,5,u)&&(p=!0),d>6&&c_(e,t,6,s)&&(p=!0),d>7&&c_(e,t,7,l)&&(p=!0),d>8&&c_(e,t,8,c)&&(p=!0),d>9&&c_(e,t,9,f)&&(p=!0),p}(e,t,n,r,o,i,a,u,s,l,c,f);case 2:return function p(e,t,n,r,o,i,a,u,s,l,c,f){var d=!1,p=t.bindings,h=p.length;if(h>0&&lp(e,t,0,n)&&(d=!0),h>1&&lp(e,t,1,r)&&(d=!0),h>2&&lp(e,t,2,o)&&(d=!0),h>3&&lp(e,t,3,i)&&(d=!0),h>4&&lp(e,t,4,a)&&(d=!0),h>5&&lp(e,t,5,u)&&(d=!0),h>6&&lp(e,t,6,s)&&(d=!0),h>7&&lp(e,t,7,l)&&(d=!0),h>8&&lp(e,t,8,c)&&(d=!0),h>9&&lp(e,t,9,f)&&(d=!0),d){var v=t.text.prefix;h>0&&(v+=m_(n,p[0])),h>1&&(v+=m_(r,p[1])),h>2&&(v+=m_(o,p[2])),h>3&&(v+=m_(i,p[3])),h>4&&(v+=m_(a,p[4])),h>5&&(v+=m_(u,p[5])),h>6&&(v+=m_(s,p[6])),h>7&&(v+=m_(l,p[7])),h>8&&(v+=m_(c,p[8])),h>9&&(v+=m_(f,p[9]));var y=Yd(e,t.nodeIndex).renderText;e.renderer.setValue(y,v)}return d}(e,t,n,r,o,i,a,u,s,l,c,f);case 16384:return function h(e,t,n,r,o,i,a,u,s,l,c,f){var d=Xd(e,t.nodeIndex),p=d.instance,h=!1,v=void 0,y=t.bindings.length;return y>0&&sp(e,t,0,n)&&(h=!0,v=wh(e,d,t,0,n,v)),y>1&&sp(e,t,1,r)&&(h=!0,v=wh(e,d,t,1,r,v)),y>2&&sp(e,t,2,o)&&(h=!0,v=wh(e,d,t,2,o,v)),y>3&&sp(e,t,3,i)&&(h=!0,v=wh(e,d,t,3,i,v)),y>4&&sp(e,t,4,a)&&(h=!0,v=wh(e,d,t,4,a,v)),y>5&&sp(e,t,5,u)&&(h=!0,v=wh(e,d,t,5,u,v)),y>6&&sp(e,t,6,s)&&(h=!0,v=wh(e,d,t,6,s,v)),y>7&&sp(e,t,7,l)&&(h=!0,v=wh(e,d,t,7,l,v)),y>8&&sp(e,t,8,c)&&(h=!0,v=wh(e,d,t,8,c,v)),y>9&&sp(e,t,9,f)&&(h=!0,v=wh(e,d,t,9,f,v)),v&&p.ngOnChanges(v),65536&t.flags&&Jd(e,256,t.nodeIndex)&&p.ngOnInit(),262144&t.flags&&p.ngDoCheck(),h}(e,t,n,r,o,i,a,u,s,l,c,f);case 32:case 64:case 128:return function v(e,t,n,r,o,i,a,u,s,l,c,f){var d=t.bindings,p=!1,h=d.length;if(h>0&&lp(e,t,0,n)&&(p=!0),h>1&&lp(e,t,1,r)&&(p=!0),h>2&&lp(e,t,2,o)&&(p=!0),h>3&&lp(e,t,3,i)&&(p=!0),h>4&&lp(e,t,4,a)&&(p=!0),h>5&&lp(e,t,5,u)&&(p=!0),h>6&&lp(e,t,6,s)&&(p=!0),h>7&&lp(e,t,7,l)&&(p=!0),h>8&&lp(e,t,8,c)&&(p=!0),h>9&&lp(e,t,9,f)&&(p=!0),p){var v=ep(e,t.nodeIndex),y=void 0;switch(201347067&t.flags){case 32:y=[],h>0&&y.push(n),h>1&&y.push(r),h>2&&y.push(o),h>3&&y.push(i),h>4&&y.push(a),h>5&&y.push(u),h>6&&y.push(s),h>7&&y.push(l),h>8&&y.push(c),h>9&&y.push(f);break;case 64:y={},h>0&&(y[d[0].name]=n),h>1&&(y[d[1].name]=r),h>2&&(y[d[2].name]=o),h>3&&(y[d[3].name]=i),h>4&&(y[d[4].name]=a),h>5&&(y[d[5].name]=u),h>6&&(y[d[6].name]=s),h>7&&(y[d[7].name]=l),h>8&&(y[d[8].name]=c),h>9&&(y[d[9].name]=f);break;case 128:var g=n;switch(h){case 1:y=g.transform(n);break;case 2:y=g.transform(r);break;case 3:y=g.transform(r,o);break;case 4:y=g.transform(r,o,i);break;case 5:y=g.transform(r,o,i,a);break;case 6:y=g.transform(r,o,i,a,u);break;case 7:y=g.transform(r,o,i,a,u,s);break;case 8:y=g.transform(r,o,i,a,u,s,l);break;case 9:y=g.transform(r,o,i,a,u,s,l,c);break;case 10:y=g.transform(r,o,i,a,u,s,l,c,f)}}v.value=y}return p}(e,t,n,r,o,i,a,u,s,l,c,f);default:throw"unreachable"}}(e,t,r,o,i,a,u,s,l,c,d,p):function v(e,t,n){switch(201347067&t.flags){case 1:return function r(e,t,n){for(var r=!1,o=0;o<n.length;o++)c_(e,t,o,n[o])&&(r=!0);return r}(e,t,n);case 2:return function o(e,t,n){for(var r=t.bindings,o=!1,i=0;i<n.length;i++)lp(e,t,i,n[i])&&(o=!0);if(o){var a="";for(i=0;i<n.length;i++)a+=m_(n[i],r[i]);a=t.text.prefix+a;var u=Yd(e,t.nodeIndex).renderText;e.renderer.setValue(u,a)}return o}(e,t,n);case 16384:return function i(e,t,n){for(var r=Xd(e,t.nodeIndex),o=r.instance,i=!1,a=void 0,u=0;u<n.length;u++)sp(e,t,u,n[u])&&(i=!0,a=wh(e,r,t,u,n[u],a));return a&&o.ngOnChanges(a),65536&t.flags&&Jd(e,256,t.nodeIndex)&&o.ngOnInit(),262144&t.flags&&o.ngDoCheck(),i}(e,t,n);case 32:case 64:case 128:return function a(e,t,n){for(var r=t.bindings,o=!1,i=0;i<n.length;i++)lp(e,t,i,n[i])&&(o=!0);if(o){var a=ep(e,t.nodeIndex),u=void 0;switch(201347067&t.flags){case 32:u=n;break;case 64:for(u={},i=0;i<n.length;i++)u[r[i].name]=n[i];break;case 128:var s=n[0],l=n.slice(1);u=s.transform.apply(s,f(l))}a.value=u}return o}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */(e,t,n);default:throw"unreachable"}}(e,t,r)}function T_(e){var t=e.def;if(4&t.nodeFlags)for(var n=0;n<t.nodes.length;n++){var r=t.nodes[n];if(4&r.flags){var o=$d(e,n).template._projectedViews;if(o)for(var i=0;i<o.length;i++){var a=o[i];a.state|=32,dp(a,e)}}else 0==(4&r.childFlags)&&(n+=r.childCount)}}function j_(e,t,n,r,o,i,a,u,s,l,c,f,d){return 0===n?function p(e,t,n,r,o,i,a,u,s,l,c,f){var d=t.bindings.length;d>0&&cp(e,t,0,n),d>1&&cp(e,t,1,r),d>2&&cp(e,t,2,o),d>3&&cp(e,t,3,i),d>4&&cp(e,t,4,a),d>5&&cp(e,t,5,u),d>6&&cp(e,t,6,s),d>7&&cp(e,t,7,l),d>8&&cp(e,t,8,c),d>9&&cp(e,t,9,f)}(e,t,r,o,i,a,u,s,l,c,f,d):function h(e,t,n){for(var r=0;r<n.length;r++)cp(e,t,r,n[r])}(e,t,r),!1}function D_(e,t){if(tp(e,t.nodeIndex).dirty)throw Zd(rp.createDebugContext(e,t.nodeIndex),"Query "+t.query.id+" not dirty","Query "+t.query.id+" dirty",0!=(1&e.state))}function A_(e){if(!(128&e.state)){if(R_(e,i_.Destroy),S_(e,i_.Destroy),bh(e,131072),e.disposables)for(var t=0;t<e.disposables.length;t++)e.disposables[t]();!function n(e){if(16&e.state){var t=hp(e);if(t){var n=t.template._projectedViews;n&&(Be(n,n.indexOf(e)),rp.dirtyParentQueries(e))}}}(e),e.renderer.destroyNode&&function r(e){for(var t=e.def.nodes.length,n=0;n<t;n++){var r=e.def.nodes[n];1&r.flags?e.renderer.destroyNode($d(e,n).renderElement):2&r.flags?e.renderer.destroyNode(Yd(e,n).renderText):(67108864&r.flags||134217728&r.flags)&&tp(e,n).destroy()}}(e),gp(e)&&e.renderer.destroy(),e.state|=128}}function S_(e,t){var n=e.def;if(33554432&n.nodeFlags)for(var r=0;r<n.nodes.length;r++){var o=n.nodes[r];33554432&o.flags?V_($d(e,r).componentView,t):0==(33554432&o.childFlags)&&(r+=o.childCount)}}function R_(e,t){var n=e.def;if(16777216&n.nodeFlags)for(var r=0;r<n.nodes.length;r++){var o=n.nodes[r];if(16777216&o.flags)for(var i=$d(e,r).viewContainer._embeddedViews,a=0;a<i.length;a++)V_(i[a],t);else 0==(16777216&o.childFlags)&&(r+=o.childCount)}}function V_(e,t){var n=e.state;switch(t){case i_.CheckNoChanges:0==(128&n)&&(12==(12&n)?O_(e):64&n&&F_(e,i_.CheckNoChangesProjectedViews));break;case i_.CheckNoChangesProjectedViews:0==(128&n)&&(32&n?O_(e):64&n&&F_(e,t));break;case i_.CheckAndUpdate:0==(128&n)&&(12==(12&n)?P_(e):64&n&&F_(e,i_.CheckAndUpdateProjectedViews));break;case i_.CheckAndUpdateProjectedViews:0==(128&n)&&(32&n?P_(e):64&n&&F_(e,t));break;case i_.Destroy:A_(e);break;case i_.CreateViewNodes:k_(e)}}function F_(e,t){R_(e,t),S_(e,t)}function M_(e,t,n,r){if(e.def.nodeFlags&t&&e.def.nodeFlags&n)for(var o=e.def.nodes.length,i=0;i<o;i++){var a=e.def.nodes[i];if(a.flags&t&&a.flags&n)switch(rp.setCurrentNode(e,a.nodeIndex),r){case 0:d_(e,a);break;case 1:D_(e,a)}a.childFlags&t&&a.childFlags&n||(i+=a.childCount)}}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */!function(e){e[e.CreateViewNodes=0]="CreateViewNodes",e[e.CheckNoChanges=1]="CheckNoChanges",e[e.CheckNoChangesProjectedViews=2]="CheckNoChangesProjectedViews",e[e.CheckAndUpdate=3]="CheckAndUpdate",e[e.CheckAndUpdateProjectedViews=4]="CheckAndUpdateProjectedViews",e[e.Destroy=5]="Destroy"}(i_||(i_={}));var H_=!1;function L_(){if(!H_){H_=!0;var e=Lr()?function t(){return{setCurrentNode:lw,createRootView:U_,createEmbeddedView:z_,createComponentView:q_,createNgModuleRef:Z_,overrideProvider:ew,overrideComponentView:tw,clearOverrides:nw,checkAndUpdateView:aw,checkNoChangesView:uw,destroyView:sw,createDebugContext:function(e,t){return new gw(e,t)},handleEvent:cw,updateDirectives:fw,updateRenderer:dw}}():function n(){return{setCurrentNode:function(){},createRootView:B_,createEmbeddedView:b_,createComponentView:I_,createNgModuleRef:th,overrideProvider:op,overrideComponentView:op,clearOverrides:op,checkAndUpdateView:P_,checkNoChangesView:O_,destroyView:A_,createDebugContext:function(e,t){return new gw(e,t)},handleEvent:function(e,t,n,r){return e.def.handleEvent(e,t,n,r)},updateDirectives:function(e,t){return e.def.updateDirectives(0===t?ow:iw,e)},updateRenderer:function(e,t){return e.def.updateRenderer(0===t?ow:iw,e)}}}();rp.setCurrentNode=e.setCurrentNode,rp.createRootView=e.createRootView,rp.createEmbeddedView=e.createEmbeddedView,rp.createComponentView=e.createComponentView,rp.createNgModuleRef=e.createNgModuleRef,rp.overrideProvider=e.overrideProvider,rp.overrideComponentView=e.overrideComponentView,rp.clearOverrides=e.clearOverrides,rp.checkAndUpdateView=e.checkAndUpdateView,rp.checkNoChangesView=e.checkNoChangesView,rp.destroyView=e.destroyView,rp.resolveDep=mh,rp.createDebugContext=e.createDebugContext,rp.handleEvent=e.handleEvent,rp.updateDirectives=e.updateDirectives,rp.updateRenderer=e.updateRenderer,rp.dirtyParentQueries=f_}}function B_(e,t,n,r,o,i){var a=o.injector.get(bd);return C_(Q_(e,o,a,t,n),r,i)}function U_(e,t,n,r,o,i){var a=o.injector.get(bd),u=Q_(e,o,new Cw(a),t,n),s=rw(r);return ww(W_.create,C_,null,[u,s,i])}function Q_(e,t,n,r,o){var i=t.injector.get(Ed),a=t.injector.get(Pr),u=n.createRenderer(null,null);return{ngModule:t,injector:e,projectableNodes:r,selectorOrNode:o,sanitizer:i,rendererFactory:n,renderer:u,errorHandler:a}}function z_(e,t,n,r){var o=rw(n);return ww(W_.create,b_,null,[e,t,o,r])}function q_(e,t,n,r){return n=X_.get(t.element.componentProvider.provider.token)||rw(n),ww(W_.create,I_,null,[e,t,n,r])}function Z_(e,t,n,r){return th(e,t,n,function o(e){var t=function n(e){var t=!1,n=!1;return 0===Y_.size?{hasOverrides:t,hasDeprecatedOverrides:n}:(e.providers.forEach((function(e){var r=Y_.get(e.token);3840&e.flags&&r&&(t=!0,n=n||r.deprecatedBehavior)})),e.modules.forEach((function(e){$_.forEach((function(r,o){A(o).providedIn===e&&(t=!0,n=n||r.deprecatedBehavior)}))})),{hasOverrides:t,hasDeprecatedOverrides:n})}(e),r=t.hasDeprecatedOverrides;return t.hasOverrides?(function o(e){for(var t=0;t<e.providers.length;t++){var n=e.providers[t];r&&(n.flags|=4096);var o=Y_.get(n.token);o&&(n.flags=-3841&n.flags|o.flags,n.deps=bp(o.deps),n.value=o.value)}if($_.size>0){var i=new Set(e.modules);$_.forEach((function(t,n){if(i.has(A(n).providedIn)){var o={token:n,flags:t.flags|(r?4096:0),deps:bp(t.deps),value:t.value,index:e.providers.length};e.providers.push(o),e.providersByKey[ap(n)]=o}}))}}(e=e.factory((function(){return op}))),e):e}(r))}var W_,K_,G_,J_,Y_=new Map,$_=new Map,X_=new Map;function ew(e){var t;Y_.set(e.token,e),"function"==typeof e.token&&(t=A(e.token))&&"function"==typeof t.providedIn&&$_.set(e.token,e)}function tw(e,t){var n=xp(zp(t)),r=xp(n.nodes[0].element.componentView);X_.set(e,r)}function nw(){Y_.clear(),$_.clear(),X_.clear()}function rw(e){if(0===Y_.size)return e;var t=function n(e){for(var t=[],n=null,r=0;r<e.nodes.length;r++){var o=e.nodes[r];1&o.flags&&(n=o),n&&3840&o.flags&&Y_.has(o.provider.token)&&(t.push(n.nodeIndex),n=null)}return t}(e);if(0===t.length)return e;e=e.factory((function(){return op}));for(var r=0;r<t.length;r++)o(e,t[r]);return e;function o(e,t){for(var n=t+1;n<e.nodes.length;n++){var r=e.nodes[n];if(1&r.flags)return;if(3840&r.flags){var o=r.provider,i=Y_.get(o.token);i&&(r.flags=-3841&r.flags|i.flags,o.deps=bp(i.deps),o.value=i.value)}}}}function ow(e,t,n,r,o,i,a,u,s,l,c,f,d){var p=e.def.nodes[t];return N_(e,p,n,r,o,i,a,u,s,l,c,f,d),224&p.flags?ep(e,t).value:void 0}function iw(e,t,n,r,o,i,a,u,s,l,c,f,d){var p=e.def.nodes[t];return j_(e,p,n,r,o,i,a,u,s,l,c,f,d),224&p.flags?ep(e,t).value:void 0}function aw(e){return ww(W_.detectChanges,P_,null,[e])}function uw(e){return ww(W_.checkNoChanges,O_,null,[e])}function sw(e){return ww(W_.destroy,A_,null,[e])}function lw(e,t){G_=e,J_=t}function cw(e,t,n,r){return lw(e,t),ww(W_.handleEvent,e.def.handleEvent,null,[e,t,n,r])}function fw(e,t){if(128&e.state)throw Kd(W_[K_]);return lw(e,vw(e,0)),e.def.updateDirectives((function n(e,r,o){for(var i=[],a=3;a<arguments.length;a++)i[a-3]=arguments[a];var u=e.def.nodes[r];return 0===t?pw(e,u,o,i):hw(e,u,o,i),16384&u.flags&&lw(e,vw(e,r)),224&u.flags?ep(e,u.nodeIndex).value:void 0}),e)}function dw(e,t){if(128&e.state)throw Kd(W_[K_]);return lw(e,yw(e,0)),e.def.updateRenderer((function n(e,r,o){for(var i=[],a=3;a<arguments.length;a++)i[a-3]=arguments[a];var u=e.def.nodes[r];return 0===t?pw(e,u,o,i):hw(e,u,o,i),3&u.flags&&lw(e,yw(e,r)),224&u.flags?ep(e,u.nodeIndex).value:void 0}),e)}function pw(e,t,n,r){if(N_.apply(void 0,f([e,t,n],r))){var o=1===n?r[0]:r;if(16384&t.flags){for(var i={},a=0;a<t.bindings.length;a++){var u=t.bindings[a],s=o[a];8&u.flags&&(i[(h=u.nonMinifiedName,"ng-reflect-"+function l(e){return e.replace(No,(function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return"-"+e[1].toLowerCase()}))}(h.replace(/[$@]/g,"_")))]=To(s))}var c=t.parent,d=$d(e,c.nodeIndex).renderElement;if(c.element.name)for(var p in i)null!=(s=i[p])?e.renderer.setAttribute(d,p,s):e.renderer.removeAttribute(d,p);else e.renderer.setValue(d,"bindings="+JSON.stringify(i,null,2))}}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
var h}function hw(e,t,n,r){j_.apply(void 0,f([e,t,n],r))}function vw(e,t){for(var n=t;n<e.def.nodes.length;n++){var r=e.def.nodes[n];if(16384&r.flags&&r.bindings&&r.bindings.length)return n}return null}function yw(e,t){for(var n=t;n<e.def.nodes.length;n++){var r=e.def.nodes[n];if(3&r.flags&&r.bindings&&r.bindings.length)return n}return null}!function(e){e[e.create=0]="create",e[e.detectChanges=1]="detectChanges",e[e.checkNoChanges=2]="checkNoChanges",e[e.destroy=3]="destroy",e[e.handleEvent=4]="handleEvent"}(W_||(W_={}));var gw=function(){function e(e,t){this.view=e,this.nodeIndex=t,null==t&&(this.nodeIndex=t=0),this.nodeDef=e.def.nodes[t];for(var n=this.nodeDef,r=e;n&&0==(1&n.flags);)n=n.parent;if(!n)for(;!n&&r;)n=vp(r),r=r.parent;this.elDef=n,this.elView=r}return Object.defineProperty(e.prototype,"elOrCompView",{get:function(){return $d(this.elView,this.elDef.nodeIndex).componentView||this.view},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"injector",{get:function(){return Xp(this.elView,this.elDef)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"component",{get:function(){return this.elOrCompView.component},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"context",{get:function(){return this.elOrCompView.context},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"providerTokens",{get:function(){var e=[];if(this.elDef)for(var t=this.elDef.nodeIndex+1;t<=this.elDef.nodeIndex+this.elDef.childCount;t++){var n=this.elView.def.nodes[t];20224&n.flags&&e.push(n.provider.token),t+=n.childCount}return e},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"references",{get:function(){var e={};if(this.elDef){_w(this.elView,this.elDef,e);for(var t=this.elDef.nodeIndex+1;t<=this.elDef.nodeIndex+this.elDef.childCount;t++){var n=this.elView.def.nodes[t];20224&n.flags&&_w(this.elView,n,e),t+=n.childCount}}return e},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"componentRenderElement",{get:function(){var e=function t(e){for(;e&&!gp(e);)e=e.parent;return e.parent?$d(e.parent,vp(e).nodeIndex):null}(this.elOrCompView);return e?e.renderElement:void 0},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"renderNode",{get:function(){return 2&this.nodeDef.flags?yp(this.view,this.nodeDef):yp(this.elView,this.elDef)},enumerable:!0,configurable:!0}),e.prototype.logError=function(e){for(var t,n,r=[],o=1;o<arguments.length;o++)r[o-1]=arguments[o];2&this.nodeDef.flags?(t=this.view.def,n=this.nodeDef.nodeIndex):(t=this.elView.def,n=this.elDef.nodeIndex);var i=mw(t,n),a=-1,u=function(){var t;return++a===i?(t=e.error).bind.apply(t,f([e],r)):op};t.factory(u),a<i&&(e.error("Illegal state: the ViewDefinitionFactory did not call the logger!"),e.error.apply(e,f(r)))},e}();function mw(e,t){for(var n=-1,r=0;r<=t;r++)3&e.nodes[r].flags&&n++;return n}function _w(e,t,n){for(var r in t.references)n[r]=h_(e,t,t.references[r])}function ww(e,t,n,r){var o=K_,i=G_,a=J_;try{K_=e;var u=t.apply(n,r);return G_=i,J_=a,K_=o,u}catch(e){if(function s(e){return!!Er(e)}(e)||!G_)throw e;throw function l(e,t){return e instanceof Error||(e=new Error(e.toString())),Wd(e,t),e}(e,bw())}}function bw(){return G_?new gw(G_,J_):null}var Cw=function(){function e(e){this.delegate=e}return e.prototype.createRenderer=function(e,t){return new Iw(this.delegate.createRenderer(e,t))},e.prototype.begin=function(){this.delegate.begin&&this.delegate.begin()},e.prototype.end=function(){this.delegate.end&&this.delegate.end()},e.prototype.whenRenderingDone=function(){return this.delegate.whenRenderingDone?this.delegate.whenRenderingDone():Promise.resolve(null)},e}(),Iw=function(){function e(e){this.delegate=e,this.debugContextFactory=bw,this.data=this.delegate.data}return e.prototype.createDebugContext=function(e){return this.debugContextFactory(e)},e.prototype.destroyNode=function(e){var t=Wm(e);!function n(e){Qm.delete(e.nativeNode)}(t),t instanceof jm&&(t.listeners.length=0),this.delegate.destroyNode&&this.delegate.destroyNode(e)},e.prototype.destroy=function(){this.delegate.destroy()},e.prototype.createElement=function(e,t){var n=this.delegate.createElement(e,t),r=this.createDebugContext(n);if(r){var o=new Dm(n,null,r);o.name=e,Jm(o)}return n},e.prototype.createComment=function(e){var t=this.delegate.createComment(e),n=this.createDebugContext(t);return n&&Jm(new jm(t,null,n)),t},e.prototype.createText=function(e){var t=this.delegate.createText(e),n=this.createDebugContext(t);return n&&Jm(new jm(t,null,n)),t},e.prototype.appendChild=function(e,t){var n=Wm(e),r=Wm(t);n&&r&&n instanceof Dm&&n.addChild(r),this.delegate.appendChild(e,t)},e.prototype.insertBefore=function(e,t,n){var r=Wm(e),o=Wm(t),i=Wm(n);r&&o&&r instanceof Dm&&r.insertBefore(i,o),this.delegate.insertBefore(e,t,n)},e.prototype.removeChild=function(e,t){var n=Wm(e),r=Wm(t);n&&r&&n instanceof Dm&&n.removeChild(r),this.delegate.removeChild(e,t)},e.prototype.selectRootElement=function(e,t){var n=this.delegate.selectRootElement(e,t),r=bw();return r&&Jm(new Dm(n,null,r)),n},e.prototype.setAttribute=function(e,t,n,r){var o=Wm(e);o&&o instanceof Dm&&(o.attributes[r?r+":"+t:t]=n),this.delegate.setAttribute(e,t,n,r)},e.prototype.removeAttribute=function(e,t,n){var r=Wm(e);r&&r instanceof Dm&&(r.attributes[n?n+":"+t:t]=null),this.delegate.removeAttribute(e,t,n)},e.prototype.addClass=function(e,t){var n=Wm(e);n&&n instanceof Dm&&(n.classes[t]=!0),this.delegate.addClass(e,t)},e.prototype.removeClass=function(e,t){var n=Wm(e);n&&n instanceof Dm&&(n.classes[t]=!1),this.delegate.removeClass(e,t)},e.prototype.setStyle=function(e,t,n,r){var o=Wm(e);o&&o instanceof Dm&&(o.styles[t]=n),this.delegate.setStyle(e,t,n,r)},e.prototype.removeStyle=function(e,t,n){var r=Wm(e);r&&r instanceof Dm&&(r.styles[t]=null),this.delegate.removeStyle(e,t,n)},e.prototype.setProperty=function(e,t,n){var r=Wm(e);r&&r instanceof Dm&&(r.properties[t]=n),this.delegate.setProperty(e,t,n)},e.prototype.listen=function(e,t,n){if("string"!=typeof e){var r=Wm(e);r&&r.listeners.push(new Tm(t,n))}return this.delegate.listen(e,t,n)},e.prototype.parentNode=function(e){return this.delegate.parentNode(e)},e.prototype.nextSibling=function(e){return this.delegate.nextSibling(e)},e.prototype.setValue=function(e,t){return this.delegate.setValue(e,t)},e}(),xw=function(e){function t(t,n,r){var o=e.call(this)||this;return o.moduleType=t,o._bootstrapComponents=n,o._ngModuleDefFactory=r,o}return o(t,e),t.prototype.create=function(e){L_();var t=function n(e){var t=Array.from(e.providers),n=Array.from(e.modules),r={};for(var o in e.providersByKey)r[o]=e.providersByKey[o];return{factory:e.factory,scope:e.scope,providers:t,modules:n,providersByKey:r}}(xp(this._ngModuleDefFactory));return rp.createNgModuleRef(this.moduleType,e||rs.NULL,this._bootstrapComponents,t)},t}(Re);
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
e.ANALYZE_FOR_ENTRY_COMPONENTS=As,e.APP_BOOTSTRAP_LISTENER=Ng,e.APP_ID=Cg,e.APP_INITIALIZER=wg,e.ApplicationInitStatus=bg,e.ApplicationModule=a_,e.ApplicationRef=mm,e.Attribute=O,e.COMPILER_OPTIONS=Zg,e.CUSTOM_ELEMENTS_SCHEMA={name:"custom-elements"},e.ChangeDetectorRef=yu,e.Compiler=qg,e.CompilerFactory=Wg,e.Component=og,e.ComponentFactory=cd,e.ComponentFactoryResolver=hd,e.ComponentRef=ld,e.ContentChild=Rs,e.ContentChildren=Ss,e.DEFAULT_CURRENCY_CODE=Ag,e.DebugElement=$m,e.DebugEventListener=Tm,e.DebugNode=Ym,e.DefaultIterableDiffer=Nd,e.Directive=rg,e.ElementRef=_d,e.EmbeddedViewRef=Nm,e.ErrorHandler=Pr,e.EventEmitter=oy,e.Host=E,e.HostBinding=sg,e.HostListener=lg,e.INJECTOR=fe,e.Inject=b,e.Injectable=Hu,e.InjectionToken=ce,e.Injector=rs,e.Input=ag,e.IterableDiffers=Fd,e.KeyValueDiffers=Md,e.LOCALE_ID=Dg,e.ModuleWithComponentFactories=Fg,e.NO_ERRORS_SCHEMA={name:"no-errors-schema"},e.NgModule=yg,e.NgModuleFactory=Re,e.NgModuleFactoryLoader=wm,e.NgModuleRef=Se,e.NgProbeToken=fm,e.NgZone=Jg,e.Optional=C,e.Output=ug,e.PACKAGE_ROOT_URL=Tg,e.PLATFORM_ID=Pg,e.PLATFORM_INITIALIZER=Og,e.Pipe=ig,e.PlatformRef=ym,e.Query=Ns,e.QueryList=ay,e.ReflectiveInjector=js,e.ReflectiveKey=ys,e.Renderer2=Id,e.RendererFactory2=bd,e.ResolvedReflectiveFactory=Cs,e.Sanitizer=Ed,e.Self=I,e.SimpleChange=Jf,e.SkipSelf=x,e.SystemJsNgModuleLoader=km,e.SystemJsNgModuleLoaderConfig=xm,e.TRANSLATIONS=Sg,e.TRANSLATIONS_FORMAT=Rg,e.TemplateRef=Ud,e.Testability=im,e.TestabilityRegistry=am,e.Type=_u,e.VERSION=kd,e.Version=Cd,e.ViewChild=Fs,e.ViewChildren=Vs,e.ViewContainerRef=zd,e.ViewRef=Pm,e.WrappedValue=Gs,e.asNativeElements=function Ew(e){return e.map((function(e){return e.nativeElement}))},e.assertPlatform=hm,e.createPlatform=dm,e.createPlatformFactory=pm,e.defineInjectable=j,e.destroyPlatform=function kw(){rm&&!rm.destroyed&&rm.destroy()},e.enableProdMode=function Ow(){if(Hr)throw new Error("Cannot enable prod mode after platform setup.");Mr=!1},e.forwardRef=z,e.getDebugNode=Wm,e.getModuleFactory=Cm,e.getPlatform=vm,e.inject=Ee,e.isDevMode=Lr,e.platformCore=Xm,e.resolveForwardRef=q,e.setTestabilityGetter=function Pw(e){um=e},e.ɵ0=w,e.ɵ1=k,e.ɵALLOW_MULTIPLE_PLATFORMS=cm,e.ɵAPP_ID_RANDOM_PROVIDER=xg,e.ɵCodegenComponentFactoryResolver=vd,e.ɵCompiler_compileModuleAndAllComponentsAsync__POST_R3__=function(e){return Promise.resolve(Ug(e))},e.ɵCompiler_compileModuleAndAllComponentsSync__POST_R3__=Ug,e.ɵCompiler_compileModuleAsync__POST_R3__=function(e){return Promise.resolve(Hg(e))},e.ɵCompiler_compileModuleSync__POST_R3__=Hg,e.ɵComponentFactory=cd,e.ɵConsole=jg,e.ɵDEFAULT_LOCALE_ID=Fh,e.ɵEMPTY_ARRAY=[],e.ɵEMPTY_MAP={},e.ɵINJECTOR_IMPL__POST_R3__=function Nw(e,t,n){return Ku({name:n},t,e,n)},e.ɵINJECTOR_SCOPE=Uu,e.ɵLifecycleHooksFeature=Lf,e.ɵNG_COMP_DEF=re,e.ɵNG_DIR_DEF=oe,e.ɵNG_ELEMENT_ID=le,e.ɵNG_INJ_DEF=F,e.ɵNG_MOD_DEF=ae,e.ɵNG_PIPE_DEF=ie,e.ɵNG_PROV_DEF=V,e.ɵNOT_FOUND_CHECK_ONLY_ELEMENT_INJECTOR=gh,e.ɵNO_CHANGE=pi,e.ɵNgModuleFactory=Sv,e.ɵNoopNgZone=om,e.ɵReflectionCapabilities=Eu,e.ɵRender3ComponentFactory=Oh,e.ɵRender3ComponentRef=Nh,e.ɵRender3NgModuleRef=Av,e.ɵSWITCH_CHANGE_DETECTOR_REF_FACTORY__POST_R3__=gu,e.ɵSWITCH_COMPILE_COMPONENT__POST_R3__=cg,e.ɵSWITCH_COMPILE_DIRECTIVE__POST_R3__=fg,e.ɵSWITCH_COMPILE_INJECTABLE__POST_R3__=function Tw(e,t){var n=null,r=null;e.hasOwnProperty(V)||(Object.defineProperty(e,V,{get:function(){return null===n&&(n=te().compileInjectable(Ae,"ng:///"+e.name+"/ɵprov.js",Ru(e,t))),n}}),e.hasOwnProperty(M)||(e[M]=function(){return e[V]})),e.hasOwnProperty(se)||Object.defineProperty(e,se,{get:function(){if(null===r){var n=Ru(e,t),o=te();r=o.compileFactory(Ae,"ng:///"+e.name+"/ɵfac.js",{name:n.name,type:n.type,typeArgumentCount:n.typeArgumentCount,deps:Tu(e),injectFn:"inject",target:o.R3FactoryTarget.Pipe})}return r},configurable:!0})},e.ɵSWITCH_COMPILE_NGMODULE__POST_R3__=gg,e.ɵSWITCH_COMPILE_PIPE__POST_R3__=dg,e.ɵSWITCH_ELEMENT_REF_FACTORY__POST_R3__=function jw(e){return du(e,Yt(),Kt())},e.ɵSWITCH_IVY_ENABLED__POST_R3__=!0,e.ɵSWITCH_RENDERER2_FACTORY__POST_R3__=function Dw(){var e=Kt(),t=Mo(Yt().index,e);return function n(e){var t=e[Ct];if(Mn(t))return t;throw new Error("Cannot inject Renderer2 when the application uses Renderer3!")}(Ft(t)?t:e)},e.ɵSWITCH_TEMPLATE_REF_FACTORY__POST_R3__=function Aw(e,t){return pu(e,t,Yt(),Kt())},e.ɵSWITCH_VIEW_CONTAINER_REF_FACTORY__POST_R3__=function Sw(e,t){return hu(e,t,Yt(),Kt())},e.ɵ_sanitizeHtml=ho,e.ɵ_sanitizeStyle=mo,e.ɵ_sanitizeUrl=zr,e.ɵallowSanitizationBypassAndThrow=Vr,e.ɵand=function Rw(e,t,n,r,o,i){e|=1;var a=wp(t);return{nodeIndex:-1,parent:null,renderParent:null,bindingIndex:-1,outputIndex:-1,flags:e,checkIndex:-1,childFlags:0,directChildFlags:0,childMatchedQueries:0,matchedQueries:a.matchedQueries,matchedQueryIds:a.matchedQueryIds,references:a.references,ngContentIndex:n,childCount:r,bindings:[],bindingFlags:0,outputs:[],element:{ns:null,name:null,attrs:null,template:i?xp(i):null,componentProvider:null,componentView:null,componentRendererType:null,publicProviders:null,allProviders:null,handleEvent:o||op},provider:null,text:null,query:null,ngContent:null}},e.ɵangular_packages_core_core_a=Z,e.ɵangular_packages_core_core_b=Ce,e.ɵangular_packages_core_core_ba=Kt,e.ɵangular_packages_core_core_bb=Yt,e.ɵangular_packages_core_core_bc=rn,e.ɵangular_packages_core_core_bd=mn,e.ɵangular_packages_core_core_bf=qv,e.ɵangular_packages_core_core_bg=Zv,e.ɵangular_packages_core_core_bh=Wv,e.ɵangular_packages_core_core_bi=Kv,e.ɵangular_packages_core_core_bj=Gv,e.ɵangular_packages_core_core_bk=xo,e.ɵangular_packages_core_core_bl=g,e.ɵangular_packages_core_core_bm=m,e.ɵangular_packages_core_core_bn=P,e.ɵangular_packages_core_core_bp=Ze,e.ɵangular_packages_core_core_bq=vi,e.ɵangular_packages_core_core_c=Pe,e.ɵangular_packages_core_core_d=Ds,e.ɵangular_packages_core_core_e=_s,e.ɵangular_packages_core_core_f=Es,e.ɵangular_packages_core_core_g=Ig,e.ɵangular_packages_core_core_h=du,e.ɵangular_packages_core_core_i=pu,e.ɵangular_packages_core_core_j=bm,e.ɵangular_packages_core_core_k=jm,e.ɵangular_packages_core_core_l=Dm,e.ɵangular_packages_core_core_m=Km,e.ɵangular_packages_core_core_n=Od,e.ɵangular_packages_core_core_o=Sd,e.ɵangular_packages_core_core_p=e_,e.ɵangular_packages_core_core_q=t_,e.ɵangular_packages_core_core_r=n_,e.ɵangular_packages_core_core_s=r_,e.ɵangular_packages_core_core_t=o_,e.ɵangular_packages_core_core_u="USD",e.ɵangular_packages_core_core_v=ch,e.ɵangular_packages_core_core_w=np,e.ɵangular_packages_core_core_x=kh,e.ɵangular_packages_core_core_y=fr,e.ɵangular_packages_core_core_z=zt,e.ɵbypassSanitizationTrustHtml=function Vw(e){return new Tr(e)},e.ɵbypassSanitizationTrustResourceUrl=function Fw(e){return new Sr(e)},e.ɵbypassSanitizationTrustScript=function Mw(e){return new Dr(e)},e.ɵbypassSanitizationTrustStyle=function Hw(e){return new jr(e)},e.ɵbypassSanitizationTrustUrl=function Lw(e){return new Ar(e)},e.ɵccf=function Bw(e,t,n,r,o,i){return new qp(e,t,n,r,o,i)},e.ɵclearOverrides=function Uw(){return L_(),rp.clearOverrides()},e.ɵclearResolutionOfComponentResourcesQueue=Us,e.ɵcmf=function Qw(e,t,n){return new xw(e,t,n)},e.ɵcompileComponent=By,e.ɵcompileDirective=Uy,e.ɵcompileNgModule=Sy,e.ɵcompileNgModuleDefs=Ry,e.ɵcompileNgModuleFactory__POST_R3__=function zw(e,t,n){var r=new Sv(n);if(function o(){return 0===Hs.size}())return Promise.resolve(r);var i=function a(e){var t=[];return e.forEach((function(e){return e&&t.push.apply(t,f(e))})),t}(e.get(Zg,[]).concat(t).map((function(e){return e.providers})));if(0===i.length)return Promise.resolve(r);var u=te(),s=rs.create({providers:i}).get(u.ResourceLoader);return Ms((function(e){return Promise.resolve(s.get(e))})).then((function(){return r}))},e.ɵcompilePipe=tg,e.ɵcreateInjector=Ku,e.ɵcrt=function qw(e){return{id:"$$undefined",styles:e.styles,encapsulation:e.encapsulation,data:e.data}},e.ɵdefaultIterableDiffers=Ld,e.ɵdefaultKeyValueDiffers=Bd,e.ɵdetectChanges=Il,e.ɵdevModeEqual=Ks,e.ɵdid=function Zw(e,t,n,r,o,i,a,u){var s=[];if(a)for(var l in a){var f=c(a[l],2);s[f[0]]={flags:8,name:l,nonMinifiedName:f[1],ns:null,securityContext:null,suffix:null}}var d=[];if(u)for(var p in u)d.push({type:1,propName:p,target:null,eventName:u[p]});return ch(e,t|=16384,n,r,o,o,i,s,d)},e.ɵeld=function Ww(t,n,r,o,i,a,u,s,l,f,d,p){var h;void 0===u&&(u=[]),f||(f=op);var v=wp(r),y=v.matchedQueries,g=v.references,m=v.matchedQueryIds,_=null,w=null;a&&(_=(h=c(jp(a),2))[0],w=h[1]),s=s||[];for(var b=[],C=0;C<s.length;C++){var I=c(s[C],3),x=I[0],E=I[2],k=c(jp(I[1]),2),O=k[0],P=k[1],N=void 0,T=void 0;switch(15&x){case 4:T=E;break;case 1:case 8:N=E}b[C]={flags:x,ns:O,name:P,nonMinifiedName:P,securityContext:N,suffix:T}}l=l||[];var j=[];for(C=0;C<l.length;C++){var D=c(l[C],2);j[C]={type:0,target:D[0],eventName:D[1],propName:null}}var A=(u=u||[]).map((function(e){var t=c(e,2),n=t[1],r=c(jp(t[0]),2);return[r[0],r[1],n]}));return p=function S(t){if(t&&"$$undefined"===t.id){var n=null!=t.encapsulation&&t.encapsulation!==e.ViewEncapsulation.None||t.styles.length||Object.keys(t.data).length;t.id=n?"c"+up++:"$$empty"}return t&&"$$empty"===t.id&&(t=null),t||null}(p),d&&(n|=33554432),{nodeIndex:-1,parent:null,renderParent:null,bindingIndex:-1,outputIndex:-1,checkIndex:t,flags:n|=1,childFlags:0,directChildFlags:0,childMatchedQueries:0,matchedQueries:y,matchedQueryIds:m,references:g,ngContentIndex:o,childCount:i,bindings:b,bindingFlags:Dp(b),outputs:j,element:{ns:_,name:w,attrs:A,template:null,componentProvider:null,componentView:d||null,componentRendererType:p,publicProviders:null,allProviders:null,handleEvent:f||op},provider:null,text:null,query:null,ngContent:null}},e.ɵfindLocaleData=Sh,e.ɵflushModuleScopingQueueAsMuchAsPossible=Dy,e.ɵgetComponentViewDefinitionFactory=zp,e.ɵgetDebugNodeR2=Gm,e.ɵgetDebugNode__POST_R3__=Zm,e.ɵgetDirectives=Of,e.ɵgetHostElement=Nf,e.ɵgetInjectableDef=A,e.ɵgetLContext=Zo,e.ɵgetLocaleCurrencyCode=function Kw(t){return Sh(t)[e.ɵLocaleDataIndex.CurrencyCode]||null},e.ɵgetLocalePluralCase=Rh,e.ɵgetModuleFactory__POST_R3__=function Gw(e){var t=jv(e);if(!t)throw Im(e);return new Sv(t)},e.ɵgetSanitizationBypassType=Fr,e.ɵglobal=ee,e.ɵinitServicesIfNeeded=L_,e.ɵinlineInterpolate=function Jw(e,t,n,r,o,i,a,u,s,l,c,f,d,p,h,v,y,g,m,_){switch(e){case 1:return t+Ap(n)+r;case 2:return t+Ap(n)+r+Ap(o)+i;case 3:return t+Ap(n)+r+Ap(o)+i+Ap(a)+u;case 4:return t+Ap(n)+r+Ap(o)+i+Ap(a)+u+Ap(s)+l;case 5:return t+Ap(n)+r+Ap(o)+i+Ap(a)+u+Ap(s)+l+Ap(c)+f;case 6:return t+Ap(n)+r+Ap(o)+i+Ap(a)+u+Ap(s)+l+Ap(c)+f+Ap(d)+p;case 7:return t+Ap(n)+r+Ap(o)+i+Ap(a)+u+Ap(s)+l+Ap(c)+f+Ap(d)+p+Ap(h)+v;case 8:return t+Ap(n)+r+Ap(o)+i+Ap(a)+u+Ap(s)+l+Ap(c)+f+Ap(d)+p+Ap(h)+v+Ap(y)+g;case 9:return t+Ap(n)+r+Ap(o)+i+Ap(a)+u+Ap(s)+l+Ap(c)+f+Ap(d)+p+Ap(h)+v+Ap(y)+g+Ap(m)+_;default:throw new Error("Does not support more than 9 expressions")}},e.ɵinterpolate=function Yw(e,t){for(var n="",r=0;r<2*e;r+=2)n=n+t[r]+Ap(t[r+1]);return n+t[2*e]},e.ɵisBoundToModule__POST_R3__=function $w(e){return e.isBoundToModule},e.ɵisDefaultChangeDetectionStrategy=function Xw(t){return null==t||t===e.ChangeDetectionStrategy.Default},e.ɵisListLikeIterable=Js,e.ɵisObservable=Zl,e.ɵisPromise=ql,e.ɵivyEnabled=Vg,e.ɵlooseIdentical=Ws,e.ɵmakeDecorator=v,e.ɵmarkDirty=xl,e.ɵmod=function eb(e){for(var t={},n=[],r=null,o=0;o<e.length;o++){var i=e[o];i.token===Uu&&(r=i.value),**********&i.flags&&n.push(i.token),i.index=o,t[ap(i.token)]=i}return{factory:null,providersByKey:t,providers:e,modules:n,scope:r}},e.ɵmpd=function tb(e,t,n,r){return n=q(n),{index:-1,deps:bp(r,B(t)),flags:e,token:t,value:n}},e.ɵncd=function nb(e,t){return{nodeIndex:-1,parent:null,renderParent:null,bindingIndex:-1,outputIndex:-1,checkIndex:-1,flags:8,childFlags:0,directChildFlags:0,childMatchedQueries:0,matchedQueries:{},matchedQueryIds:0,references:{},ngContentIndex:e,childCount:0,bindings:[],bindingFlags:0,outputs:[],element:null,provider:null,text:null,query:null,ngContent:{index:t}}},e.ɵnov=function rb(e,t){var n=e.def.nodes[t];if(1&n.flags){var r=$d(e,n.nodeIndex);return n.element.template?r.template:r.renderElement}if(2&n.flags)return Yd(e,n.nodeIndex).renderText;if(20240&n.flags)return Xd(e,n.nodeIndex).instance;throw new Error("Illegal state: read nodeValue for node index "+t)},e.ɵoverrideComponentView=function ob(e,t){return L_(),rp.overrideComponentView(e,t)},e.ɵoverrideProvider=
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
function ib(e){return L_(),rp.overrideProvider(e)},e.ɵpad=function ab(e,t){return y_(32,e,Ue(t))},e.ɵpatchComponentDefWithScope=Fy,e.ɵpid=function ub(e,t,n){return ch(-1,e|=16,null,0,t,t,n)},e.ɵpod=function sb(e,t){for(var n=Object.keys(t),r=n.length,o=[],i=0;i<r;i++)o.push(n[i]);return y_(64,e,o)},e.ɵppd=function lb(e,t){return y_(128,e,Ue(t+1))},e.ɵprd=function cb(e,t,n,r,o){return ch(-1,e,t,0,n,r,o)},e.ɵpublishDefaultGlobalUtils=function fb(){Rf||(Rf=!0,Vf("getComponent",Cf),Vf("getContext",If),Vf("getListeners",Tf),Vf("getOwningComponent",xf),Vf("getHostElement",Nf),Vf("getInjector",kf),Vf("getRootComponents",Ef),Vf("getDirectives",Of),Vf("applyChanges",Af))},e.ɵpublishGlobalUtil=Vf,e.ɵqud=function db(e,t,n){var r=[];for(var o in n)r.push({propName:o,bindingType:n[o]});return{nodeIndex:-1,parent:null,renderParent:null,bindingIndex:-1,outputIndex:-1,checkIndex:-1,flags:e,childFlags:0,directChildFlags:0,childMatchedQueries:0,ngContentIndex:-1,matchedQueries:{},matchedQueryIds:0,references:{},childCount:0,bindings:[],bindingFlags:0,outputs:[],element:null,provider:null,text:null,query:{id:t,filterId:_p(t),bindings:r},ngContent:null}},e.ɵregisterLocaleData=function pb(t,n,r){"string"!=typeof n&&(r=n,n=t[e.ɵLocaleDataIndex.LocaleId]),n=n.toLowerCase().replace(/_/g,"-"),Ah[n]=t,r&&(Ah[n][e.ɵLocaleDataIndex.ExtraData]=r)},e.ɵregisterModuleFactory=function hb(e,t){var n=Pv.get(e);Nv(e,n&&n.moduleType,t.moduleType),Pv.set(e,t)},e.ɵregisterNgModuleType=Tv,e.ɵrenderComponent=function vb(e,t){void 0===t&&(t={});var n=t.rendererFactory||Hn,r=t.sanitizer||null,o=at(e);o.type!=e&&(o.type=e);var i,a=o.selectors[0][0],u=ea(n.createRenderer(null,null),t.host||a,o.encapsulation),s=o.onPush?576:528,l=Hf(t.scheduler,t.playerHandler),c=n.createRenderer(u,o),f=Xi(0,-1,null,1,0,null,null,null,null,null),d=Ui(null,f,l,s,null,null,n,c,void 0,t.injector||null);hn(d,null);try{n.begin&&n.begin(),i=Mf(Ff(u,o,d,n,c,null,r),o,d,l,t.hostFeatures||null),qi(f,d,null),Zi(f,d,null,null)}finally{gn(),n.end&&n.end()}return i},e.ɵresetCompiledComponents=function yb(){new Map,new Map,Ty.length=0},e.ɵresolveComponentResources=Ms,e.ɵsetClassMetadata=
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
function gb(e,t,n,r){return Ze((function(){var o,a=e,u=a.prototype?Object.getPrototypeOf(a.prototype):null,s=u&&u.constructor;null!==t&&(void 0===a.decorators||s&&s.decorators===a.decorators?a.decorators=t:(o=a.decorators).push.apply(o,f(t))),null!==n&&(a.ctorParameters=n),null!==r&&(a.propDecorators=void 0===a.propDecorators||s&&s.propDecorators===a.propDecorators?r:i(i({},a.propDecorators),r))}))},e.ɵsetCurrentInjector=we,e.ɵsetDocument=function mb(e){Vn=e},e.ɵsetLocaleId=Ov,e.ɵstore=Nl,e.ɵstringify=B,e.ɵted=function _b(e,t,n){for(var r=[],o=1;o<n.length;o++)r[o-1]={flags:8,name:null,ns:null,nonMinifiedName:null,securityContext:null,suffix:n[o]};return{nodeIndex:-1,parent:null,renderParent:null,bindingIndex:-1,outputIndex:-1,checkIndex:e,flags:2,childFlags:0,directChildFlags:0,childMatchedQueries:0,matchedQueries:{},matchedQueryIds:0,references:{},ngContentIndex:t,childCount:0,bindings:r,bindingFlags:8,outputs:[],element:null,provider:null,text:{prefix:n[0]},query:null,ngContent:null}},e.ɵtransitiveScopesFor=My,e.ɵunregisterLocaleData=function wb(){Ah={}},e.ɵunv=function bb(e,t,n,r){if(Gs.isWrapped(r)){r=Gs.unwrap(r);var o=e.def.nodes[t].bindingIndex+n,i=Gs.unwrap(e.oldValues[o]);e.oldValues[o]=new Gs(i)}return r},e.ɵunwrapSafeValue=Rr,e.ɵvid=function Cb(e,t,n,r){for(var o=0,i=0,a=0,u=0,s=0,l=null,c=null,f=!1,d=!1,p=null,h=0;h<t.length;h++){var v=t[h];if(v.nodeIndex=h,v.parent=l,v.bindingIndex=o,v.outputIndex=i,v.renderParent=c,a|=v.flags,s|=v.matchedQueryIds,v.element){var y=v.element;y.publicProviders=l?l.element.publicProviders:Object.create(null),y.allProviders=y.publicProviders,f=!1,d=!1,v.element.template&&(s|=v.element.template.nodeMatchedQueries)}if(w_(l,v,t.length),o+=v.bindings.length,i+=v.outputs.length,!c&&3&v.flags&&(p=v),20224&v.flags){f||(f=!0,l.element.publicProviders=Object.create(l.element.publicProviders),l.element.allProviders=l.element.publicProviders);var g=0!=(32768&v.flags);0==(8192&v.flags)||g?l.element.publicProviders[ap(v.provider.token)]=v:(d||(d=!0,l.element.allProviders=Object.create(l.element.publicProviders)),l.element.allProviders[ap(v.provider.token)]=v),g&&(l.element.componentProvider=v)}if(l?(l.childFlags|=v.flags,l.directChildFlags|=v.flags,l.childMatchedQueries|=v.matchedQueryIds,v.element&&v.element.template&&(l.childMatchedQueries|=v.element.template.nodeMatchedQueries)):u|=v.flags,v.childCount>0)l=v,__(v)||(c=v);else for(;l&&h===l.nodeIndex+l.childCount;){var m=l.parent;m&&(m.childFlags|=l.childFlags,m.childMatchedQueries|=l.childMatchedQueries),c=(l=m)&&__(l)?l.renderParent:l}}return{factory:null,nodeFlags:a,rootNodeFlags:u,nodeMatchedQueries:s,flags:e,nodes:t,updateDirectives:n||op,updateRenderer:r||op,handleEvent:function(e,n,r,o){return t[n].element.handleEvent(e,r,o)},bindingCount:o,outputCount:i,lastRenderRootNode:p}},e.ɵwhenRendered=function Ib(e){return vi(e).clean},e.ɵɵCopyDefinitionFeature=Gf,e.ɵɵInheritDefinitionFeature=Uf,e.ɵɵNgOnChangesFeature=Yf,e.ɵɵProvidersFeature=sd,e.ɵɵadvance=yi,e.ɵɵattribute=ol,e.ɵɵattributeInterpolate1=hl,e.ɵɵattributeInterpolate2=vl,e.ɵɵattributeInterpolate3=yl,e.ɵɵattributeInterpolate4=gl,e.ɵɵattributeInterpolate5=ml,e.ɵɵattributeInterpolate6=_l,e.ɵɵattributeInterpolate7=wl,e.ɵɵattributeInterpolate8=bl,e.ɵɵattributeInterpolateV=Cl,e.ɵɵclassMap=jc,e.ɵɵclassMapInterpolate1=tf,e.ɵɵclassMapInterpolate2=nf,e.ɵɵclassMapInterpolate3=rf,e.ɵɵclassMapInterpolate4=of,e.ɵɵclassMapInterpolate5=af,e.ɵɵclassMapInterpolate6=uf,e.ɵɵclassMapInterpolate7=sf,e.ɵɵclassMapInterpolate8=lf,e.ɵɵclassMapInterpolateV=cf,e.ɵɵclassProp=Pc,e.ɵɵcomponentHostSyntheticListener=Kl,e.ɵɵcontainer=El,e.ɵɵcontainerRefreshEnd=Pl,e.ɵɵcontainerRefreshStart=Ol,e.ɵɵcontentQuery=_y,e.ɵɵdefaultStyleSanitizer=ko,e.ɵɵdefineComponent=Je,e.ɵɵdefineDirective=ot,e.ɵɵdefineInjectable=T,e.ɵɵdefineInjector=D,e.ɵɵdefineNgModule=tt,e.ɵɵdefinePipe=it,e.ɵɵdirectiveInject=jl,e.ɵɵdisableBindings=Wt,e.ɵɵelement=Ml,e.ɵɵelementContainer=Bl,e.ɵɵelementContainerEnd=Ll,e.ɵɵelementContainerStart=Hl,e.ɵɵelementEnd=Fl,e.ɵɵelementStart=Vl,e.ɵɵembeddedViewEnd=Ql,e.ɵɵembeddedViewStart=Ul,e.ɵɵenableBindings=Zt,e.ɵɵgetCurrentView=zl,e.ɵɵgetFactoryOf=wr,e.ɵɵgetInheritedFactory=br,e.ɵɵhostProperty=wf,e.ɵɵi18n=vv,e.ɵɵi18nApply=wv,e.ɵɵi18nAttributes=yv,e.ɵɵi18nEnd=fv,e.ɵɵi18nExp=_v,e.ɵɵi18nPostprocess=cv,e.ɵɵi18nStart=sv,e.ɵɵinject=Ie,e.ɵɵinjectAttribute=Dl,e.ɵɵinjectPipeChangeDetectorRef=Oy,e.ɵɵinvalidFactory=Al,e.ɵɵinvalidFactoryDep=xe,e.ɵɵlistener=Wl,e.ɵɵloadQuery=Cy,e.ɵɵnamespaceHTML=In,e.ɵɵnamespaceMathML=Cn,e.ɵɵnamespaceSVG=bn,e.ɵɵnextContext=$l,e.ɵɵpipe=Jv,e.ɵɵpipeBind1=Yv,e.ɵɵpipeBind2=$v,e.ɵɵpipeBind3=Xv,e.ɵɵpipeBind4=ey,e.ɵɵpipeBindV=ty,e.ɵɵprojection=rc,e.ɵɵprojectionDef=ec,e.ɵɵproperty=Sl,e.ɵɵpropertyInterpolate=oc,e.ɵɵpropertyInterpolate1=ic,e.ɵɵpropertyInterpolate2=ac,e.ɵɵpropertyInterpolate3=uc,e.ɵɵpropertyInterpolate4=sc,e.ɵɵpropertyInterpolate5=lc,e.ɵɵpropertyInterpolate6=cc,e.ɵɵpropertyInterpolate7=fc,e.ɵɵpropertyInterpolate8=dc,e.ɵɵpropertyInterpolateV=pc,e.ɵɵpureFunction0=Rv,e.ɵɵpureFunction1=Vv,e.ɵɵpureFunction2=Fv,e.ɵɵpureFunction3=Mv,e.ɵɵpureFunction4=Hv,e.ɵɵpureFunction5=Lv,e.ɵɵpureFunction6=Bv,e.ɵɵpureFunction7=Uv,e.ɵɵpureFunction8=Qv,e.ɵɵpureFunctionV=zv,e.ɵɵqueryRefresh=vy,e.ɵɵreference=Tl,e.ɵɵresolveBody=er,e.ɵɵresolveDocument=Xn,e.ɵɵresolveWindow=$n,e.ɵɵrestoreView=Jt,e.ɵɵsanitizeHtml=_o,e.ɵɵsanitizeResourceUrl=Co,e.ɵɵsanitizeScript=Io,e.ɵɵsanitizeStyle=wo,e.ɵɵsanitizeUrl=bo,e.ɵɵsanitizeUrlOrResourceUrl=Eo,e.ɵɵselect=gi,e.ɵɵsetComponentScope=Ye,e.ɵɵsetNgModuleScope=nt,e.ɵɵstaticContentQuery=wy,e.ɵɵstaticViewQuery=yy,e.ɵɵstyleMap=Nc,e.ɵɵstyleProp=Oc,e.ɵɵstylePropInterpolate1=ff,e.ɵɵstylePropInterpolate2=df,e.ɵɵstylePropInterpolate3=pf,e.ɵɵstylePropInterpolate4=hf,e.ɵɵstylePropInterpolate5=vf,e.ɵɵstylePropInterpolate6=yf,e.ɵɵstylePropInterpolate7=gf,e.ɵɵstylePropInterpolate8=mf,e.ɵɵstylePropInterpolateV=_f,e.ɵɵstyleSanitizer=kc,e.ɵɵtemplate=kl,e.ɵɵtemplateRefExtractor=ky,e.ɵɵtext=zc,e.ɵɵtextInterpolate=qc,e.ɵɵtextInterpolate1=Zc,e.ɵɵtextInterpolate2=Wc,e.ɵɵtextInterpolate3=Kc,e.ɵɵtextInterpolate4=Gc,e.ɵɵtextInterpolate5=Jc,e.ɵɵtextInterpolate6=Yc,e.ɵɵtextInterpolate7=$c,e.ɵɵtextInterpolate8=Xc,e.ɵɵtextInterpolateV=ef,e.ɵɵupdateSyntheticHostBinding=bf,e.ɵɵviewQuery=gy,Object.defineProperty(e,"__esModule",{value:!0})}));