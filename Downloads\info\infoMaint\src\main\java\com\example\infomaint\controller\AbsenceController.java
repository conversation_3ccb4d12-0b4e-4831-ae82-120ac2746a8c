package com.example.infomaint.controller;

import com.example.infomaint.model.Absence;
import com.example.infomaint.service.AbsenceService;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/absences")
@RequiredArgsConstructor
@CrossOrigin(origins = "http://localhost:4200")
public class AbsenceController {
    
    private final AbsenceService absenceService;
    
    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('HR')")
    public ResponseEntity<List<Absence>> getAllAbsences() {
        List<Absence> absences = absenceService.getAllAbsences();
        return ResponseEntity.ok(absences);
    }
    
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('HR') or @absenceService.getAbsenceById(#id).orElse(null)?.user?.id == authentication.principal.id")
    public ResponseEntity<Absence> getAbsenceById(@PathVariable String id) {
        return absenceService.getAbsenceById(id)
                .map(absence -> ResponseEntity.ok(absence))
                .orElse(ResponseEntity.notFound().build());
    }
    
    @GetMapping("/user/{userId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('HR') or #userId == authentication.principal.id")
    public ResponseEntity<List<Absence>> getAbsencesByUser(@PathVariable String userId) {
        List<Absence> absences = absenceService.getAbsencesByUser(userId);
        return ResponseEntity.ok(absences);
    }
    
    @GetMapping("/type/{type}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('HR')")
    public ResponseEntity<List<Absence>> getAbsencesByType(@PathVariable Absence.AbsenceType type) {
        List<Absence> absences = absenceService.getAbsencesByType(type);
        return ResponseEntity.ok(absences);
    }
    
    @GetMapping("/status/{status}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('HR')")
    public ResponseEntity<List<Absence>> getAbsencesByStatus(@PathVariable Absence.AbsenceStatus status) {
        List<Absence> absences = absenceService.getAbsencesByStatus(status);
        return ResponseEntity.ok(absences);
    }
    
    @PostMapping
    @PreAuthorize("hasRole('EMPLOYEE') or hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<Absence> createAbsence(@Valid @RequestBody Absence absence) {
        try {
            Absence createdAbsence = absenceService.createAbsence(absence);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdAbsence);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('HR') or @absenceService.getAbsenceById(#id).orElse(null)?.user?.id == authentication.principal.id")
    public ResponseEntity<Absence> updateAbsence(@PathVariable String id, @Valid @RequestBody Absence absenceDetails) {
        try {
            Absence updatedAbsence = absenceService.updateAbsence(id, absenceDetails);
            return ResponseEntity.ok(updatedAbsence);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    @PutMapping("/{id}/approve")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('HR')")
    public ResponseEntity<Absence> approveAbsence(@PathVariable String id, @RequestBody Map<String, String> approval) {
        try {
            String approverId = approval.get("approverId");
            String comments = approval.get("comments");
            Absence updatedAbsence = absenceService.approveAbsence(id, approverId, comments);
            return ResponseEntity.ok(updatedAbsence);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    @PutMapping("/{id}/reject")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('HR')")
    public ResponseEntity<Absence> rejectAbsence(@PathVariable String id, @RequestBody Map<String, String> rejection) {
        try {
            String approverId = rejection.get("approverId");
            String reason = rejection.get("reason");
            Absence updatedAbsence = absenceService.rejectAbsence(id, approverId, reason);
            return ResponseEntity.ok(updatedAbsence);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    @PutMapping("/{id}/cancel")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('HR') or @absenceService.getAbsenceById(#id).orElse(null)?.user?.id == authentication.principal.id")
    public ResponseEntity<Absence> cancelAbsence(@PathVariable String id) {
        try {
            Absence updatedAbsence = absenceService.cancelAbsence(id);
            return ResponseEntity.ok(updatedAbsence);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('HR') or @absenceService.getAbsenceById(#id).orElse(null)?.user?.id == authentication.principal.id")
    public ResponseEntity<Void> deleteAbsence(@PathVariable String id) {
        try {
            absenceService.deleteAbsence(id);
            return ResponseEntity.noContent().build();
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    @GetMapping("/date-range")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('HR')")
    public ResponseEntity<List<Absence>> getAbsencesByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        List<Absence> absences = absenceService.getAbsencesByDateRange(startDate, endDate);
        return ResponseEntity.ok(absences);
    }
    
    @GetMapping("/on-date")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('HR')")
    public ResponseEntity<List<Absence>> getAbsencesOnDate(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        List<Absence> absences = absenceService.getAbsencesOnDate(date);
        return ResponseEntity.ok(absences);
    }
    
    @GetMapping("/user/{userId}/balance/{type}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('HR') or #userId == authentication.principal.id")
    public ResponseEntity<Integer> getUserLeaveBalance(@PathVariable String userId, @PathVariable Absence.AbsenceType type) {
        Integer balance = absenceService.getUserLeaveBalance(userId, type);
        return ResponseEntity.ok(balance);
    }
}
