/**
 * @fileoverview added by tsickle
 * Generated from: packages/core/src/core_private_export.ts
 * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc
 */
/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export { ALLOW_MULTIPLE_PLATFORMS as ɵALLOW_MULTIPLE_PLATFORMS } from './application_ref';
export { APP_ID_RANDOM_PROVIDER as ɵAPP_ID_RANDOM_PROVIDER } from './application_tokens';
export { defaultIterableDiffers as ɵdefaultIterableDiffers, defaultKeyValueDiffers as ɵdefaultKeyValueDiffers } from './change_detection/change_detection';
export { devModeEqual as ɵdevModeEqual, isListLikeIterable as ɵisListLikeIterable } from './change_detection/change_detection_util';
export { ChangeDetectorStatus as ɵChangeDetectorStatus, isDefaultChangeDetectionStrategy as ɵisDefaultChangeDetectionStrategy } from './change_detection/constants';
export { Console as ɵConsole } from './console';
export { getDebugNodeR2 as ɵgetDebugNodeR2 } from './debug/debug_node';
export { inject, setCurrentInjector as ɵsetCurrentInjector, ɵɵinject } from './di/injector_compatibility';
export { getInjectableDef as ɵgetInjectableDef } from './di/interface/defs';
export { INJECTOR_SCOPE as ɵINJECTOR_SCOPE } from './di/scope';
export { LocaleDataIndex as ɵLocaleDataIndex, findLocaleData as ɵfindLocaleData, getLocaleCurrencyCode as ɵgetLocaleCurrencyCode, getLocalePluralCase as ɵgetLocalePluralCase, registerLocaleData as ɵregisterLocaleData, unregisterAllLocaleData as ɵunregisterLocaleData } from './i18n/locale_data_api';
export { DEFAULT_LOCALE_ID as ɵDEFAULT_LOCALE_ID } from './i18n/localization';
export { ivyEnabled as ɵivyEnabled } from './ivy_switch';
export { ComponentFactory as ɵComponentFactory } from './linker/component_factory';
export { CodegenComponentFactoryResolver as ɵCodegenComponentFactoryResolver } from './linker/component_factory_resolver';
export { clearResolutionOfComponentResourcesQueue as ɵclearResolutionOfComponentResourcesQueue, resolveComponentResources as ɵresolveComponentResources } from './metadata/resource_loading';
export { ReflectionCapabilities as ɵReflectionCapabilities } from './reflection/reflection_capabilities';
export { allowSanitizationBypassAndThrow as ɵallowSanitizationBypassAndThrow, getSanitizationBypassType as ɵgetSanitizationBypassType, unwrapSafeValue as ɵunwrapSafeValue } from './sanitization/bypass';
export { _sanitizeHtml as ɵ_sanitizeHtml } from './sanitization/html_sanitizer';
export { _sanitizeStyle as ɵ_sanitizeStyle } from './sanitization/style_sanitizer';
export { _sanitizeUrl as ɵ_sanitizeUrl } from './sanitization/url_sanitizer';
export { looseIdentical as ɵlooseIdentical, } from './util/comparison';
export { makeDecorator as ɵmakeDecorator } from './util/decorators';
export { global as ɵglobal } from './util/global';
export { isObservable as ɵisObservable, isPromise as ɵisPromise } from './util/lang';
export { stringify as ɵstringify } from './util/stringify';
export { clearOverrides as ɵclearOverrides, initServicesIfNeeded as ɵinitServicesIfNeeded, overrideComponentView as ɵoverrideComponentView, overrideProvider as ɵoverrideProvider } from './view/index';
export { NOT_FOUND_CHECK_ONLY_ELEMENT_INJECTOR as ɵNOT_FOUND_CHECK_ONLY_ELEMENT_INJECTOR } from './view/provider';
//# sourceMappingURL=data:application/json;base64,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