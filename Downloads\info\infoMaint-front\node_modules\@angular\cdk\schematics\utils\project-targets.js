"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTargetsByBuilderName = exports.getProjectTargetOptions = exports.defaultTargetBuilders = void 0;
const schematics_1 = require("@angular-devkit/schematics");
/** Object that maps a CLI target to its default builder name. */
exports.defaultTargetBuilders = {
    build: '@angular-devkit/build-angular:browser',
    test: '@angular-devkit/build-angular:karma',
};
/** Resolves the architect options for the build target of the given project. */
function getProjectTargetOptions(project, buildTarget) {
    var _a, _b;
    const options = (_b = (_a = project.targets) === null || _a === void 0 ? void 0 : _a.get(buildTarget)) === null || _b === void 0 ? void 0 : _b.options;
    if (!options) {
        throw new schematics_1.SchematicsException(`Cannot determine project target configuration for: ${buildTarget}.`);
    }
    return options;
}
exports.getProjectTargetOptions = getProjectTargetOptions;
/** Gets all targets from the given project that match the specified builder name. */
function getTargetsByBuilderName(project, builderName) {
    return Array.from(project.targets.keys())
        .filter(name => { var _a; return ((_a = project.targets.get(name)) === null || _a === void 0 ? void 0 : _a.builder) === builderName; })
        .map(name => project.targets.get(name));
}
exports.getTargetsByBuilderName = getTargetsByBuilderName;
//# sourceMappingURL=data:application/json;base64,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