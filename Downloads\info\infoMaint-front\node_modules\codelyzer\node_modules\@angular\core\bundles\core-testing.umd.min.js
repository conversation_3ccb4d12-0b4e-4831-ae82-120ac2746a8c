/**
 * @license Angular v9.0.0
 * (c) 2010-2020 Google LLC. https://angular.io/
 * License: MIT
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("@angular/core"),require("@angular/compiler")):"function"==typeof define&&define.amd?define("@angular/core/testing",["exports","@angular/core","@angular/compiler"],t):t(((e=e||self).ng=e.ng||{},e.ng.core=e.ng.core||{},e.ng.core.testing={}),e.ng.core,e.ng.compiler)}(this,(function(e,t,r){"use strict";
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var n="undefined"==typeof window?global:window;function o(e,t,r,n){var o=Zone.current,i=Zone.AsyncTestZoneSpec;if(void 0===i)throw new Error("AsyncTestZoneSpec is needed for the async() test helper but could not be found. Please make sure that your environment includes zone.js/dist/async-test.js");var s=Zone.ProxyZoneSpec;if(void 0===s)throw new Error("ProxyZoneSpec is needed for the async() test helper but could not be found. Please make sure that your environment includes zone.js/dist/proxy.js");var u=s.get();s.assertPresent();var c=Zone.current.getZoneWith("ProxyZoneSpec"),l=u.getDelegate();return c.parent.run((function(){var e=new i((function(){o.run((function(){u.getDelegate()==e&&u.setDelegate(l),r()}))}),(function(t){o.run((function(){u.getDelegate()==e&&u.setDelegate(l),n(t)}))}),"test");u.setDelegate(e)})),Zone.current.runGuarded(e,t)}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
var i=function(){function e(e,r,n){var o=this;this.componentRef=e,this.ngZone=r,this._autoDetect=n,this._isStable=!0,this._isDestroyed=!1,this._resolve=null,this._promise=null,this._onUnstableSubscription=null,this._onStableSubscription=null,this._onMicrotaskEmptySubscription=null,this._onErrorSubscription=null,this.changeDetectorRef=e.changeDetectorRef,this.elementRef=e.location,this.debugElement=t.getDebugNode(this.elementRef.nativeElement),this.componentInstance=e.instance,this.nativeElement=this.elementRef.nativeElement,this.componentRef=e,this.ngZone=r,r&&r.runOutsideAngular((function(){o._onUnstableSubscription=r.onUnstable.subscribe({next:function(){o._isStable=!1}}),o._onMicrotaskEmptySubscription=r.onMicrotaskEmpty.subscribe({next:function(){o._autoDetect&&o.detectChanges(!0)}}),o._onStableSubscription=r.onStable.subscribe({next:function(){o._isStable=!0,null!==o._promise&&function e(t){Zone.current.scheduleMicroTask("scheduleMicrotask",t)}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */((function(){r.hasPendingMacrotasks||null!==o._promise&&(o._resolve(!0),o._resolve=null,o._promise=null)}))}}),o._onErrorSubscription=r.onError.subscribe({next:function(e){throw e}})}))}return e.prototype._tick=function(e){this.changeDetectorRef.detectChanges(),e&&this.checkNoChanges()},e.prototype.detectChanges=function(e){var t=this;void 0===e&&(e=!0),null!=this.ngZone?this.ngZone.run((function(){t._tick(e)})):this._tick(e)},e.prototype.checkNoChanges=function(){this.changeDetectorRef.checkNoChanges()},e.prototype.autoDetectChanges=function(e){if(void 0===e&&(e=!0),null==this.ngZone)throw new Error("Cannot call autoDetectChanges when ComponentFixtureNoNgZone is set");this._autoDetect=e,this.detectChanges()},e.prototype.isStable=function(){return this._isStable&&!this.ngZone.hasPendingMacrotasks},e.prototype.whenStable=function(){var e=this;return this.isStable()?Promise.resolve(!1):null!==this._promise?this._promise:(this._promise=new Promise((function(t){e._resolve=t})),this._promise)},e.prototype._getRenderer=function(){return void 0===this._renderer&&(this._renderer=this.componentRef.injector.get(t.RendererFactory2,null)),this._renderer},e.prototype.whenRenderingDone=function(){var e=this._getRenderer();return e&&e.whenRenderingDone?e.whenRenderingDone():this.whenStable()},e.prototype.destroy=function(){this._isDestroyed||(this.componentRef.destroy(),null!=this._onUnstableSubscription&&(this._onUnstableSubscription.unsubscribe(),this._onUnstableSubscription=null),null!=this._onStableSubscription&&(this._onStableSubscription.unsubscribe(),this._onStableSubscription=null),null!=this._onMicrotaskEmptySubscription&&(this._onMicrotaskEmptySubscription.unsubscribe(),this._onMicrotaskEmptySubscription=null),null!=this._onErrorSubscription&&(this._onErrorSubscription.unsubscribe(),this._onErrorSubscription=null),this._isDestroyed=!0)},e}(),s="undefined"!=typeof Zone?Zone:null,u=s&&s.FakeAsyncTestZoneSpec,c=s&&s.ProxyZoneSpec,l=null;function a(){l=null,c&&c.assertPresent().resetDelegate()}var p=!1;function d(){if(null==l)throw new Error("The code should be running in the fakeAsync zone to call this function");return l}function h(){d().flushMicrotasks()}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var f="undefined"!=typeof Zone?Zone:null,v=f&&f[f.__symbol__("fakeAsyncTest")];function m(){return v?v.resetFakeAsyncZone():a()}
/*! *****************************************************************************
    Copyright (c) Microsoft Corporation. All rights reserved.
    Licensed under the Apache License, Version 2.0 (the "License"); you may not use
    this file except in compliance with the License. You may obtain a copy of the
    License at http://www.apache.org/licenses/LICENSE-2.0

    THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
    WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
    MERCHANTABLITY OR NON-INFRINGEMENT.

    See the Apache Version 2.0 License for specific language governing permissions
    and limitations under the License.
    ***************************************************************************** */
var y=function(e,t){return(y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)};function g(e,t){function r(){this.constructor=e}y(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var _=function(){return(_=Object.assign||function e(t){for(var r,n=1,o=arguments.length;n<o;n++)for(var i in r=arguments[n])Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i]);return t}).apply(this,arguments)};function M(e,t,r,n){var o,i=arguments.length,s=i<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,r,n);else for(var u=e.length-1;u>=0;u--)(o=e[u])&&(s=(i<3?o(s):i>3?o(t,r,s):o(t,r))||s);return i>3&&s&&Object.defineProperty(t,r,s),s}function O(e,t,r,n){return new(r||(r=Promise))((function(o,i){function s(e){try{c(n.next(e))}catch(e){i(e)}}function u(e){try{c(n.throw(e))}catch(e){i(e)}}function c(e){e.done?o(e.value):new r((function(t){t(e.value)})).then(s,u)}c((n=n.apply(e,t||[])).next())}))}function b(e,t){var r,n,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(i){return function(u){return function c(i){if(r)throw new TypeError("Generator is already executing.");for(;s;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return s.label++,{value:i[1],done:!1};case 5:s.label++,n=i[1],i=[0];continue;case 7:i=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){s=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){s.label=i[1];break}if(6===i[0]&&s.label<o[1]){s.label=o[1],o=i;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(i);break}o[2]&&s.ops.pop(),s.trys.pop();continue}i=t.call(e,s)}catch(e){i=[6,e],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}}function T(e){var t="function"==typeof Symbol&&e[Symbol.iterator],r=0;return t?t.call(e):{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}}}function w(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)s.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return s}function C(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(w(arguments[t]));return e}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
var E=function(){function e(){var e=this;this._promise=new Promise((function(t,r){e._resolve=t,e._reject=r}))}return e.prototype.done=function(e){this._resolve(e)},e.prototype.fail=function(e,t){this._reject(e)},Object.defineProperty(e.prototype,"promise",{get:function(){return this._promise},enumerable:!0,configurable:!0}),e}(),P=function(){function e(){}return e.prototype.insertRootElement=function(e){},e}(),D=new t.InjectionToken("ComponentFixtureAutoDetect"),S=new t.InjectionToken("ComponentFixtureNoNgZone");
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
function N(e){var t=[],r=new Map;function n(t){var n=r.get(t);if(!n){var o=e(t);r.set(t,n=o.then(I))}return n}return j.forEach((function(e,r){var o=[];e.templateUrl&&o.push(n(e.templateUrl).then((function(t){e.template=t})));var i=e.styleUrls,s=e.styles||(e.styles=[]),u=e.styles.length;i&&i.forEach((function(t,r){s.push(""),o.push(n(t).then((function(n){s[u+r]=n,i.splice(i.indexOf(t),1),0==i.length&&(e.styleUrls=void 0)})))}));var c=Promise.all(o).then((function(){return function e(t){F.delete(t)}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */(r)}));t.push(c)})),A(),Promise.all(t).then((function(){}))}var j=new Map,F=new Set;function R(e){return F.has(e)}function A(){var e=j;return j=new Map,e}function I(e){return"string"==typeof e?e:e.text()}var k=0,x=function(){function e(){this._references=new Map}return e.prototype.overrideMetadata=function(e,r,n){var o={};if(r&&function i(e){var t=[];Object.keys(e).forEach((function(e){e.startsWith("_")||t.push(e)}));for(var r=e;r=Object.getPrototypeOf(r);)Object.keys(r).forEach((function(e){var n=Object.getOwnPropertyDescriptor(r,e);!e.startsWith("_")&&n&&"get"in n&&t.push(e)}));return t}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */(r).forEach((function(e){return o[e]=r[e]})),n.set){if(n.remove||n.add)throw new Error("Cannot set and add/remove "+t.ɵstringify(e)+" at the same time!");!function s(e,t){for(var r in t)e[r]=t[r]}(o,n.set)}return n.remove&&function u(e,t,r){var n=new Set,o=function(e){var o=t[e];Array.isArray(o)?o.forEach((function(t){n.add(Z(e,t,r))})):n.add(Z(e,o,r))};for(var i in t)o(i);var s=function(t){var o=e[t];Array.isArray(o)?e[t]=o.filter((function(e){return!n.has(Z(t,e,r))})):n.has(Z(t,o,r))&&(e[t]=void 0)};for(var i in e)s(i)}(o,n.remove,this._references),n.add&&function c(e,t){for(var r in t){var n=t[r],o=e[r];e[r]=null!=o&&Array.isArray(o)?o.concat(n):n}}(o,n.add),new e(o)},e}();function Z(e,r,n){return e+":"+JSON.stringify(r,(function(e,r){return"function"==typeof r&&(r=function o(e,r){var n=r.get(e);return n||(n=""+t.ɵstringify(e)+k++,r.set(e,n)),n}(r,n)),r}))}var U,L=new t.ɵReflectionCapabilities,B=function(){function e(){this.overrides=new Map,this.resolved=new Map}return e.prototype.addOverride=function(e,t){var r=this.overrides.get(e)||[];r.push(t),this.overrides.set(e,r),this.resolved.delete(e)},e.prototype.setOverrides=function(e){var t=this;this.overrides.clear(),e.forEach((function(e){var r=w(e,2);t.addOverride(r[0],r[1])}))},e.prototype.getAnnotation=function(e){for(var r=L.annotations(e),n=r.length-1;n>=0;n--){var o=r[n];if(o instanceof t.Directive||o instanceof t.Component||o instanceof t.Pipe||o instanceof t.NgModule)return o instanceof this.type?o:null}return null},e.prototype.resolve=function(e){var t=this,r=this.resolved.get(e)||null;if(!r){if(r=this.getAnnotation(e)){var n=this.overrides.get(e);if(n){var o=new x;n.forEach((function(e){r=o.overrideMetadata(t.type,r,e)}))}}this.resolved.set(e,r)}return r},e}(),G=function(e){function r(){return null!==e&&e.apply(this,arguments)||this}return g(r,e),Object.defineProperty(r.prototype,"type",{get:function(){return t.Directive},enumerable:!0,configurable:!0}),r}(B),V=function(e){function r(){return null!==e&&e.apply(this,arguments)||this}return g(r,e),Object.defineProperty(r.prototype,"type",{get:function(){return t.Component},enumerable:!0,configurable:!0}),r}(B),q=function(e){function r(){return null!==e&&e.apply(this,arguments)||this}return g(r,e),Object.defineProperty(r.prototype,"type",{get:function(){return t.Pipe},enumerable:!0,configurable:!0}),r}(B),W=function(e){function r(){return null!==e&&e.apply(this,arguments)||this}return g(r,e),Object.defineProperty(r.prototype,"type",{get:function(){return t.NgModule},enumerable:!0,configurable:!0}),r}(B);!function(e){e[e.DECLARATION=0]="DECLARATION",e[e.OVERRIDE_TEMPLATE=1]="OVERRIDE_TEMPLATE"}(U||(U={}));var z=function(){function e(e,t){this.platform=e,this.additionalModuleTypes=t,this.originalComponentResolutionQueue=null,this.declarations=[],this.imports=[],this.providers=[],this.schemas=[],this.pendingComponents=new Set,this.pendingDirectives=new Set,this.pendingPipes=new Set,this.seenComponents=new Set,this.seenDirectives=new Set,this.existingComponentStyles=new Map,this.resolvers=function r(){return{module:new W,component:new V,directive:new G,pipe:new q}}(),this.componentToModuleScope=new Map,this.initialNgDefs=new Map,this.defCleanupOps=[],this._injector=null,this.compilerProviders=null,this.providerOverrides=[],this.rootProviderOverrides=[],this.providerOverridesByModule=new Map,this.providerOverridesByToken=new Map,this.moduleProvidersOverridden=new Set,this.testModuleRef=null,this.hasModuleOverrides=!1,this.testModuleType=function n(){}}return e.prototype.setCompilerProviders=function(e){this.compilerProviders=e,this._injector=null},e.prototype.configureTestingModule=function(e){var t,r,n,o;void 0!==e.declarations&&(this.queueTypeArray(e.declarations,U.DECLARATION),(t=this.declarations).push.apply(t,C(e.declarations))),void 0!==e.imports&&(this.queueTypesFromModulesArray(e.imports),(r=this.imports).push.apply(r,C(e.imports))),void 0!==e.providers&&(n=this.providers).push.apply(n,C(e.providers)),void 0!==e.schemas&&(o=this.schemas).push.apply(o,C(e.schemas))},e.prototype.overrideModule=function(e,t){this.hasModuleOverrides=!0,this.resolvers.module.addOverride(e,t);var r=this.resolvers.module.resolve(e);if(null===r)throw X(e.name,"NgModule");this.recompileNgModule(e,r),this.queueTypesFromModulesArray([e])},e.prototype.overrideComponent=function(e,t){this.resolvers.component.addOverride(e,t),this.pendingComponents.add(e)},e.prototype.overrideDirective=function(e,t){this.resolvers.directive.addOverride(e,t),this.pendingDirectives.add(e)},e.prototype.overridePipe=function(e,t){this.resolvers.pipe.addOverride(e,t),this.pendingPipes.add(e)},e.prototype.overrideProvider=function(e,r){var n;n=void 0!==r.useFactory?{provide:e,useFactory:r.useFactory,deps:r.deps||[],multi:r.multi}:void 0!==r.useValue?{provide:e,useValue:r.useValue,multi:r.multi}:{provide:e};var o="string"!=typeof e?t.ɵgetInjectableDef(e):null;if((null!==o&&"root"===o.providedIn?this.rootProviderOverrides:this.providerOverrides).push(n),this.providerOverridesByToken.set(e,n),null!==o&&null!==o.providedIn&&"string"!=typeof o.providedIn){var i=this.providerOverridesByModule.get(o.providedIn);void 0!==i?i.push(n):this.providerOverridesByModule.set(o.providedIn,[n])}},e.prototype.overrideTemplateUsingTestingModule=function(e,r){var n,o=e[t.ɵNG_COMP_DEF],i=!!o&&!R(e)&&!!(n=this.resolvers.component.resolve(e)).styleUrls&&n.styleUrls.length>0;this.overrideComponent(e,{set:i?{template:r,styles:[],styleUrls:[]}:{template:r}}),i&&o.styles&&o.styles.length>0&&this.existingComponentStyles.set(e,o.styles),this.componentToModuleScope.set(e,U.OVERRIDE_TEMPLATE)},e.prototype.compileComponents=function(){return O(this,void 0,void 0,(function(){var e,t=this;return b(this,(function(n){switch(n.label){case 0:return this.clearComponentResolutionQueue(),this.compileTypesSync()?[4,N((function(n){return e||(e=t.injector.get(r.ResourceLoader)),Promise.resolve(e.get(n))}))]:[3,2];case 1:n.sent(),n.label=2;case 2:return[2]}}))}))},e.prototype.finalize=function(){this.compileTypesSync(),this.compileTestModule(),this.applyTransitiveScopes(),this.applyProviderOverrides(),this.patchComponentsWithExistingStyles(),this.componentToModuleScope.clear(),this.testModuleRef=new t.ɵRender3NgModuleRef(this.testModuleType,this.platform.injector);var e=this.testModuleRef.injector.get(t.LOCALE_ID,t.ɵDEFAULT_LOCALE_ID);return t.ɵsetLocaleId(e),this.testModuleRef.injector.get(t.ApplicationInitStatus).runInitializers(),this.testModuleRef},e.prototype._compileNgModuleSync=function(e){this.queueTypesFromModulesArray([e]),this.compileTypesSync(),this.applyProviderOverrides(),this.applyProviderOverridesToModule(e),this.applyTransitiveScopes()},e.prototype._compileNgModuleAsync=function(e){return O(this,void 0,void 0,(function(){return b(this,(function(t){switch(t.label){case 0:return this.queueTypesFromModulesArray([e]),[4,this.compileComponents()];case 1:return t.sent(),this.applyProviderOverrides(),this.applyProviderOverridesToModule(e),this.applyTransitiveScopes(),[2]}}))}))},e.prototype._getModuleResolver=function(){return this.resolvers.module},e.prototype._getComponentFactories=function(e){var r=this;return J(e.ɵmod.declarations).reduce((function(e,n){var o=n.ɵcmp;return o&&e.push(new t.ɵRender3ComponentFactory(o,r.testModuleRef)),e}),[])},e.prototype.compileTypesSync=function(){var e=this,r=!1;return this.pendingComponents.forEach((function(n){r=r||R(n);var o=e.resolvers.component.resolve(n);if(null===o)throw X(n.name,"Component");e.maybeStoreNgDef(t.ɵNG_COMP_DEF,n),t.ɵcompileComponent(n,o)})),this.pendingComponents.clear(),this.pendingDirectives.forEach((function(r){var n=e.resolvers.directive.resolve(r);if(null===n)throw X(r.name,"Directive");e.maybeStoreNgDef(t.ɵNG_DIR_DEF,r),t.ɵcompileDirective(r,n)})),this.pendingDirectives.clear(),this.pendingPipes.forEach((function(r){var n=e.resolvers.pipe.resolve(r);if(null===n)throw X(r.name,"Pipe");e.maybeStoreNgDef(t.ɵNG_PIPE_DEF,r),t.ɵcompilePipe(r,n)})),this.pendingPipes.clear(),r},e.prototype.applyTransitiveScopes=function(){var e=this,r=new Map;this.componentToModuleScope.forEach((function(n,o){var i=function(n){if(!r.has(n)){var o=function i(e){return e===U.DECLARATION||e===U.OVERRIDE_TEMPLATE}(n);r.set(n,t.ɵtransitiveScopesFor(o?e.testModuleType:n,!o&&e.hasModuleOverrides))}return r.get(n)}(n);e.storeFieldOfDefOnType(o,t.ɵNG_COMP_DEF,"directiveDefs"),e.storeFieldOfDefOnType(o,t.ɵNG_COMP_DEF,"pipeDefs"),t.ɵpatchComponentDefWithScope(o.ɵcmp,i)})),this.componentToModuleScope.clear()},e.prototype.applyProviderOverrides=function(){var e=this,r=function(r){return function(n){var o=(r===t.ɵNG_COMP_DEF?e.resolvers.component:e.resolvers.directive).resolve(n);e.hasProviderOverrides(o.providers)&&e.patchDefWithProviderOverrides(n,r)}};this.seenComponents.forEach(r(t.ɵNG_COMP_DEF)),this.seenDirectives.forEach(r(t.ɵNG_DIR_DEF)),this.seenComponents.clear(),this.seenDirectives.clear()},e.prototype.applyProviderOverridesToModule=function(e){var r,n,o,i;if(!this.moduleProvidersOverridden.has(e)){this.moduleProvidersOverridden.add(e);var s=e[t.ɵNG_INJ_DEF];if(this.providerOverridesByToken.size>0){var u=C(s.providers,this.providerOverridesByModule.get(e)||[]);this.hasProviderOverrides(u)&&(this.maybeStoreNgDef(t.ɵNG_INJ_DEF,e),this.storeFieldOfDefOnType(e,t.ɵNG_INJ_DEF,"providers"),s.providers=this.getOverriddenProviders(u));var c=J(e[t.ɵNG_MOD_DEF].imports);try{for(var l=T(c),a=l.next();!a.done;a=l.next())this.applyProviderOverridesToModule(h=a.value)}catch(e){r={error:e}}finally{try{a&&!a.done&&(n=l.return)&&n.call(l)}finally{if(r)throw r.error}}try{for(var p=T(H(s.imports)),d=p.next();!d.done;d=p.next()){var h;(h=d.value).hasOwnProperty("ngModule")&&(this.defCleanupOps.push({object:h,fieldName:"providers",originalValue:h.providers}),h.providers=this.getOverriddenProviders(h.providers))}}catch(e){o={error:e}}finally{try{d&&!d.done&&(i=p.return)&&i.call(p)}finally{if(o)throw o.error}}}}},e.prototype.patchComponentsWithExistingStyles=function(){this.existingComponentStyles.forEach((function(e,r){return r[t.ɵNG_COMP_DEF].styles=e})),this.existingComponentStyles.clear()},e.prototype.queueTypeArray=function(e,t){var r,n;try{for(var o=T(e),i=o.next();!i.done;i=o.next()){var s=i.value;Array.isArray(s)?this.queueTypeArray(s,t):this.queueType(s,t)}}catch(e){r={error:e}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}},e.prototype.recompileNgModule=function(e,r){this.maybeStoreNgDef(t.ɵNG_MOD_DEF,e),this.maybeStoreNgDef(t.ɵNG_INJ_DEF,e),t.ɵcompileNgModuleDefs(e,r)},e.prototype.queueType=function(e,r){return this.resolvers.component.resolve(e)?(!R(e)&&e.hasOwnProperty(t.ɵNG_COMP_DEF)||this.pendingComponents.add(e),this.seenComponents.add(e),void(this.componentToModuleScope.has(e)&&this.componentToModuleScope.get(e)!==U.DECLARATION||this.componentToModuleScope.set(e,r))):this.resolvers.directive.resolve(e)?(e.hasOwnProperty(t.ɵNG_DIR_DEF)||this.pendingDirectives.add(e),void this.seenDirectives.add(e)):void(!this.resolvers.pipe.resolve(e)||e.hasOwnProperty(t.ɵNG_PIPE_DEF)||this.pendingPipes.add(e))},e.prototype.queueTypesFromModulesArray=function(e){var t=this,r=new Set,n=function(e){var o,i;try{for(var s=T(e),u=s.next();!u.done;u=s.next()){var c=u.value;if(Array.isArray(c))n(c);else if(Q(c)){var l=c.ɵmod;if(r.has(l))continue;r.add(l),t.queueTypeArray(J(l.declarations),c),n(J(l.imports)),n(J(l.exports))}}}catch(e){o={error:e}}finally{try{u&&!u.done&&(i=s.return)&&i.call(s)}finally{if(o)throw o.error}}};n(e)},e.prototype.maybeStoreNgDef=function(e,t){if(!this.initialNgDefs.has(t)){var r=Object.getOwnPropertyDescriptor(t,e);this.initialNgDefs.set(t,[e,r])}},e.prototype.storeFieldOfDefOnType=function(e,t,r){var n=e[t];this.defCleanupOps.push({object:n,fieldName:r,originalValue:n[r]})},e.prototype.clearComponentResolutionQueue=function(){var e=this;null===this.originalComponentResolutionQueue&&(this.originalComponentResolutionQueue=new Map),A().forEach((function(t,r){return e.originalComponentResolutionQueue.set(r,t)}))},e.prototype.restoreComponentResolutionQueue=function(){null!==this.originalComponentResolutionQueue&&(function e(t){F.clear(),t.forEach((function(e,t){return F.add(t)})),j=t}(this.originalComponentResolutionQueue),this.originalComponentResolutionQueue=null)},e.prototype.restoreOriginalState=function(){K(this.defCleanupOps,(function(e){e.object[e.fieldName]=e.originalValue})),this.initialNgDefs.forEach((function(e,t){var r=w(e,2),n=r[0],o=r[1];o?Object.defineProperty(t,n,o):delete t[n]})),this.initialNgDefs.clear(),this.moduleProvidersOverridden.clear(),this.restoreComponentResolutionQueue(),t.ɵsetLocaleId(t.ɵDEFAULT_LOCALE_ID)},e.prototype.compileTestModule=function(){var e,r=this;t.ɵcompileNgModuleDefs(e=function e(){},{providers:C(this.rootProviderOverrides)});var n=new t.NgZone({enableLongStackTrace:!0}),o=C([{provide:t.NgZone,useValue:n},{provide:t.Compiler,useFactory:function(){return new ee(r)}}],this.providers,this.providerOverrides);t.ɵcompileNgModuleDefs(this.testModuleType,{declarations:this.declarations,imports:[e,this.additionalModuleTypes,this.imports||[]],schemas:this.schemas,providers:o},!0),this.applyProviderOverridesToModule(this.testModuleType)},Object.defineProperty(e.prototype,"injector",{get:function(){if(null!==this._injector)return this._injector;var e,r=[];this.platform.injector.get(t.COMPILER_OPTIONS).forEach((function(e){e.providers&&r.push(e.providers)})),null!==this.compilerProviders&&r.push.apply(r,C(this.compilerProviders)),t.ɵcompileNgModuleDefs(e=function e(){},{providers:r});var n=new t.ɵNgModuleFactory(e);return this._injector=n.create(this.platform.injector).injector,this._injector},enumerable:!0,configurable:!0}),e.prototype.getSingleProviderOverrides=function(e){var t=Y(e);return this.providerOverridesByToken.get(t)||null},e.prototype.getProviderOverrides=function(e){var t=this;return e&&e.length&&0!==this.providerOverridesByToken.size?H(H(e,(function(e){return t.getSingleProviderOverrides(e)||[]}))):[]},e.prototype.getOverriddenProviders=function(e){var t=this;if(!e||!e.length||0===this.providerOverridesByToken.size)return[];var r=H(e),n=C(r,this.getProviderOverrides(r)),o=[],i=new Set;return K(n,(function(e){var r=Y(e);t.providerOverridesByToken.has(r)?i.has(r)||(i.add(r),o.unshift(_(_({},e),{multi:!1}))):o.unshift(e)})),o},e.prototype.hasProviderOverrides=function(e){return this.getProviderOverrides(e).length>0},e.prototype.patchDefWithProviderOverrides=function(e,t){var r=this,n=e[t];if(n&&n.providersResolver){this.maybeStoreNgDef(t,e);var o=n.providersResolver,i=function(e){return r.getOverriddenProviders(e)};this.storeFieldOfDefOnType(e,t,"providersResolver"),n.providersResolver=function(e){return o(e,i)}}},e}();function Q(e){return e.hasOwnProperty("ɵmod")}function J(e){return e instanceof Function?e():e}function H(e,t){var r=[];return e.forEach((function(e){Array.isArray(e)?r.push.apply(r,C(H(e,t))):r.push(t?t(e):e)})),r}function Y(e){return function t(e,r){return e&&"object"==typeof e&&e[r]}(e,"provide")||e}function K(e,t){for(var r=e.length-1;r>=0;r--)t(e[r],r)}function X(e,t){return new Error(e+" class doesn't have @"+t+" decorator or is missing metadata.")}var $,ee=function(){function e(e){this.testBed=e}return e.prototype.compileModuleSync=function(e){return this.testBed._compileNgModuleSync(e),new t.ɵNgModuleFactory(e)},e.prototype.compileModuleAsync=function(e){return O(this,void 0,void 0,(function(){return b(this,(function(r){switch(r.label){case 0:return[4,this.testBed._compileNgModuleAsync(e)];case 1:return r.sent(),[2,new t.ɵNgModuleFactory(e)]}}))}))},e.prototype.compileModuleAndAllComponentsSync=function(e){var r=this.compileModuleSync(e),n=this.testBed._getComponentFactories(e);return new t.ModuleWithComponentFactories(r,n)},e.prototype.compileModuleAndAllComponentsAsync=function(e){return O(this,void 0,void 0,(function(){var r,n;return b(this,(function(o){switch(o.label){case 0:return[4,this.compileModuleAsync(e)];case 1:return r=o.sent(),n=this.testBed._getComponentFactories(e),[2,new t.ModuleWithComponentFactories(r,n)]}}))}))},e.prototype.clearCache=function(){},e.prototype.clearCacheFor=function(e){},e.prototype.getModuleId=function(e){var t=this.testBed._getModuleResolver().resolve(e);return t&&t.id||void 0},e}(),te=0,re=function(){function e(){this.platform=null,this.ngModule=null,this._compiler=null,this._testModuleRef=null,this._activeFixtures=[],this._globalCompilationChecked=!1}return e.initTestEnvironment=function(e,t,r){var n=ne();return n.initTestEnvironment(e,t,r),n},e.resetTestEnvironment=function(){ne().resetTestEnvironment()},e.configureCompiler=function(t){return ne().configureCompiler(t),e},e.configureTestingModule=function(t){return ne().configureTestingModule(t),e},e.compileComponents=function(){return ne().compileComponents()},e.overrideModule=function(t,r){return ne().overrideModule(t,r),e},e.overrideComponent=function(t,r){return ne().overrideComponent(t,r),e},e.overrideDirective=function(t,r){return ne().overrideDirective(t,r),e},e.overridePipe=function(t,r){return ne().overridePipe(t,r),e},e.overrideTemplate=function(t,r){return ne().overrideComponent(t,{set:{template:r,templateUrl:null}}),e},e.overrideTemplateUsingTestingModule=function(t,r){return ne().overrideTemplateUsingTestingModule(t,r),e},e.overrideProvider=function(t,r){return ne().overrideProvider(t,r),e},e.inject=function(e,t,r){return ne().inject(e,t,r)},e.get=function(e,r,n){return void 0===r&&(r=t.Injector.THROW_IF_NOT_FOUND),void 0===n&&(n=t.InjectFlags.Default),ne().inject(e,r,n)},e.createComponent=function(e){return ne().createComponent(e)},e.resetTestingModule=function(){return ne().resetTestingModule(),e},e.prototype.initTestEnvironment=function(e,t,r){if(this.platform||this.ngModule)throw new Error("Cannot set base providers because it has already been called");this.platform=t,this.ngModule=e,this._compiler=new z(this.platform,this.ngModule)},e.prototype.resetTestEnvironment=function(){this.resetTestingModule(),this._compiler=null,this.platform=null,this.ngModule=null},e.prototype.resetTestingModule=function(){this.checkGlobalCompilationFinished(),t.ɵresetCompiledComponents(),null!==this._compiler&&this.compiler.restoreOriginalState(),this._compiler=new z(this.platform,this.ngModule),this._testModuleRef=null,this.destroyActiveFixtures()},e.prototype.configureCompiler=function(e){if(null!=e.useJit)throw new Error("the Render3 compiler JiT mode is not configurable !");void 0!==e.providers&&this.compiler.setCompilerProviders(e.providers)},e.prototype.configureTestingModule=function(e){this.assertNotInstantiated("R3TestBed.configureTestingModule","configure the test module"),this.compiler.configureTestingModule(e)},e.prototype.compileComponents=function(){return this.compiler.compileComponents()},e.prototype.inject=function(t,r,n){if(t===e)return this;var o={},i=this.testModuleRef.injector.get(t,o,n);return i===o?this.compiler.injector.get(t,r,n):i},e.prototype.get=function(e,r,n){return void 0===r&&(r=t.Injector.THROW_IF_NOT_FOUND),void 0===n&&(n=t.InjectFlags.Default),this.inject(e,r,n)},e.prototype.execute=function(e,t,r){var n=this,o=e.map((function(e){return n.inject(e)}));return t.apply(r,o)},e.prototype.overrideModule=function(e,t){this.assertNotInstantiated("overrideModule","override module metadata"),this.compiler.overrideModule(e,t)},e.prototype.overrideComponent=function(e,t){this.assertNotInstantiated("overrideComponent","override component metadata"),this.compiler.overrideComponent(e,t)},e.prototype.overrideTemplateUsingTestingModule=function(e,t){this.assertNotInstantiated("R3TestBed.overrideTemplateUsingTestingModule","Cannot override template when the test module has already been instantiated"),this.compiler.overrideTemplateUsingTestingModule(e,t)},e.prototype.overrideDirective=function(e,t){this.assertNotInstantiated("overrideDirective","override directive metadata"),this.compiler.overrideDirective(e,t)},e.prototype.overridePipe=function(e,t){this.assertNotInstantiated("overridePipe","override pipe metadata"),this.compiler.overridePipe(e,t)},e.prototype.overrideProvider=function(e,t){this.compiler.overrideProvider(e,t)},e.prototype.createComponent=function(e){var r=this,n=this.inject(P),o="root-ng-internal-isolated-"+te++;n.insertRootElement(o);var s=e.ɵcmp;if(!s)throw new Error("It looks like '"+t.ɵstringify(e)+"' has not been IVY compiled - it has no 'ɵcmp' field");var u=this.inject(S,!1),c=this.inject(D,!1),l=u?null:this.inject(t.NgZone,null),a=new t.ɵRender3ComponentFactory(s),p=function(){var e=a.create(t.Injector.NULL,[],"#"+o,r.testModuleRef);return new i(e,l,c)},d=l?l.run(p):p();return this._activeFixtures.push(d),d},Object.defineProperty(e.prototype,"compiler",{get:function(){if(null===this._compiler)throw new Error("Need to call TestBed.initTestEnvironment() first");return this._compiler},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"testModuleRef",{get:function(){return null===this._testModuleRef&&(this._testModuleRef=this.compiler.finalize()),this._testModuleRef},enumerable:!0,configurable:!0}),e.prototype.assertNotInstantiated=function(e,t){if(null!==this._testModuleRef)throw new Error("Cannot "+t+" when the test module has already been instantiated. Make sure you are not using `inject` before `"+e+"`.")},e.prototype.checkGlobalCompilationFinished=function(){this._globalCompilationChecked||null!==this._testModuleRef||t.ɵflushModuleScopingQueueAsMuchAsPossible(),this._globalCompilationChecked=!0},e.prototype.destroyActiveFixtures=function(){this._activeFixtures.forEach((function(e){try{e.destroy()}catch(t){console.error("Error during cleanup of component",{component:e.componentInstance,stacktrace:t})}})),this._activeFixtures=[]},e}();
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function ne(){return $=$||new re}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function oe(){throw Error("unimplemented")}var ie,se=function(e){function r(){return null!==e&&e.apply(this,arguments)||this}return g(r,e),Object.defineProperty(r.prototype,"injector",{get:function(){throw oe()},enumerable:!0,configurable:!0}),r.prototype.overrideModule=function(e,t){throw oe()},r.prototype.overrideDirective=function(e,t){throw oe()},r.prototype.overrideComponent=function(e,t){throw oe()},r.prototype.overridePipe=function(e,t){throw oe()},r.prototype.loadAotSummaries=function(e){throw oe()},r.prototype.getComponentFactory=function(e){throw oe()},r.prototype.getComponentFromError=function(e){throw oe()},M([t.Injectable()],r)}(t.Compiler),ue=function ue(){},ce=0,le=function(){function e(){this._instantiated=!1,this._compiler=null,this._moduleRef=null,this._moduleFactory=null,this._compilerOptions=[],this._moduleOverrides=[],this._componentOverrides=[],this._directiveOverrides=[],this._pipeOverrides=[],this._providers=[],this._declarations=[],this._imports=[],this._schemas=[],this._activeFixtures=[],this._testEnvAotSummaries=function(){return[]},this._aotSummaries=[],this._templateOverrides=[],this._isRoot=!0,this._rootProviderOverrides=[],this.platform=null,this.ngModule=null}return e.initTestEnvironment=function(e,t,r){var n=de();return n.initTestEnvironment(e,t,r),n},e.resetTestEnvironment=function(){de().resetTestEnvironment()},e.resetTestingModule=function(){return de().resetTestingModule(),e},e.configureCompiler=function(t){return de().configureCompiler(t),e},e.configureTestingModule=function(t){return de().configureTestingModule(t),e},e.compileComponents=function(){return pe().compileComponents()},e.overrideModule=function(t,r){return de().overrideModule(t,r),e},e.overrideComponent=function(t,r){return de().overrideComponent(t,r),e},e.overrideDirective=function(t,r){return de().overrideDirective(t,r),e},e.overridePipe=function(t,r){return de().overridePipe(t,r),e},e.overrideTemplate=function(t,r){return de().overrideComponent(t,{set:{template:r,templateUrl:null}}),e},e.overrideTemplateUsingTestingModule=function(t,r){return de().overrideTemplateUsingTestingModule(t,r),e},e.overrideProvider=function(t,r){return de().overrideProvider(t,r),e},e.inject=function(e,t,r){return de().inject(e,t,r)},e.get=function(e,r,n){return void 0===r&&(r=t.Injector.THROW_IF_NOT_FOUND),void 0===n&&(n=t.InjectFlags.Default),de().inject(e,r,n)},e.createComponent=function(e){return de().createComponent(e)},e.prototype.initTestEnvironment=function(e,t,r){if(this.platform||this.ngModule)throw new Error("Cannot set base providers because it has already been called");this.platform=t,this.ngModule=e,r&&(this._testEnvAotSummaries=r)},e.prototype.resetTestEnvironment=function(){this.resetTestingModule(),this.platform=null,this.ngModule=null,this._testEnvAotSummaries=function(){return[]}},e.prototype.resetTestingModule=function(){t.ɵclearOverrides(),this._aotSummaries=[],this._templateOverrides=[],this._compiler=null,this._moduleOverrides=[],this._componentOverrides=[],this._directiveOverrides=[],this._pipeOverrides=[],this._isRoot=!0,this._rootProviderOverrides=[],this._moduleRef=null,this._moduleFactory=null,this._compilerOptions=[],this._providers=[],this._declarations=[],this._imports=[],this._schemas=[],this._instantiated=!1,this._activeFixtures.forEach((function(e){try{e.destroy()}catch(t){console.error("Error during cleanup of component",{component:e.componentInstance,stacktrace:t})}})),this._activeFixtures=[]},e.prototype.configureCompiler=function(e){this._assertNotInstantiated("TestBed.configureCompiler","configure the compiler"),this._compilerOptions.push(e)},e.prototype.configureTestingModule=function(e){var t,r,n,o;this._assertNotInstantiated("TestBed.configureTestingModule","configure the test module"),e.providers&&(t=this._providers).push.apply(t,C(e.providers)),e.declarations&&(r=this._declarations).push.apply(r,C(e.declarations)),e.imports&&(n=this._imports).push.apply(n,C(e.imports)),e.schemas&&(o=this._schemas).push.apply(o,C(e.schemas)),e.aotSummaries&&this._aotSummaries.push(e.aotSummaries)},e.prototype.compileComponents=function(){var e=this;if(this._moduleFactory||this._instantiated)return Promise.resolve(null);var t=this._createCompilerAndModule();return this._compiler.compileModuleAndAllComponentsAsync(t).then((function(t){e._moduleFactory=t.ngModuleFactory}))},e.prototype._initIfNeeded=function(){var e,r;if(!this._instantiated){if(!this._moduleFactory)try{var n=this._createCompilerAndModule();this._moduleFactory=this._compiler.compileModuleAndAllComponentsSync(n).ngModuleFactory}catch(e){var o=this._compiler.getComponentFromError(e);throw o?new Error("This test module uses the component "+t.ɵstringify(o)+' which is using a "templateUrl" or "styleUrls", but they were never compiled. Please call "TestBed.compileComponents" before your test.'):e}try{for(var i=T(this._templateOverrides),s=i.next();!s.done;s=i.next()){var u=s.value,c=u.component,l=this._compiler.getComponentFactory(u.templateOf);t.ɵoverrideComponentView(c,l)}}catch(t){e={error:t}}finally{try{s&&!s.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}var a=new t.NgZone({enableLongStackTrace:!0,shouldCoalesceEventChangeDetection:!1}),p=t.Injector.create({providers:[{provide:t.NgZone,useValue:a}],parent:this.platform.injector,name:this._moduleFactory.moduleType.name});this._moduleRef=this._moduleFactory.create(p),this._moduleRef.injector.get(t.ApplicationInitStatus).runInitializers(),this._instantiated=!0}},e.prototype._createCompilerAndModule=function(){var e,r,n=this,o=this._providers.concat([{provide:ae,useValue:this}]),i=C(this._declarations,this._templateOverrides.map((function(e){return e.templateOf}))),s=[];if(this._isRoot){var u=M([t.NgModule({providers:C(this._rootProviderOverrides),jit:!0})],(function u(){}));s.push(u)}o.push({provide:t.ɵINJECTOR_SCOPE,useValue:this._isRoot?"root":null});var c=M([t.NgModule({providers:o,declarations:i,imports:[s,this.ngModule,this._imports],schemas:this._schemas,jit:!0})],(function c(){})),l=this.platform.injector.get(ue);this._compiler=l.createTestingCompiler(this._compilerOptions);try{for(var a=T(C([this._testEnvAotSummaries],this._aotSummaries)),p=a.next();!p.done;p=a.next())this._compiler.loadAotSummaries(p.value)}catch(t){e={error:t}}finally{try{p&&!p.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}return this._moduleOverrides.forEach((function(e){return n._compiler.overrideModule(e[0],e[1])})),this._componentOverrides.forEach((function(e){return n._compiler.overrideComponent(e[0],e[1])})),this._directiveOverrides.forEach((function(e){return n._compiler.overrideDirective(e[0],e[1])})),this._pipeOverrides.forEach((function(e){return n._compiler.overridePipe(e[0],e[1])})),c},e.prototype._assertNotInstantiated=function(e,t){if(this._instantiated)throw new Error("Cannot "+t+" when the test module has already been instantiated. Make sure you are not using `inject` before `"+e+"`.")},e.prototype.inject=function(e,t,r){if(this._initIfNeeded(),e===ae)return this;var n={},o=this._moduleRef.injector.get(e,n,r);return o===n?this._compiler.injector.get(e,t,r):o},e.prototype.get=function(e,r,n){return void 0===r&&(r=t.Injector.THROW_IF_NOT_FOUND),void 0===n&&(n=t.InjectFlags.Default),this.inject(e,r,n)},e.prototype.execute=function(e,t,r){var n=this;this._initIfNeeded();var o=e.map((function(e){return n.inject(e)}));return t.apply(r,o)},e.prototype.overrideModule=function(e,t){this._assertNotInstantiated("overrideModule","override module metadata"),this._moduleOverrides.push([e,t])},e.prototype.overrideComponent=function(e,t){this._assertNotInstantiated("overrideComponent","override component metadata"),this._componentOverrides.push([e,t])},e.prototype.overrideDirective=function(e,t){this._assertNotInstantiated("overrideDirective","override directive metadata"),this._directiveOverrides.push([e,t])},e.prototype.overridePipe=function(e,t){this._assertNotInstantiated("overridePipe","override pipe metadata"),this._pipeOverrides.push([e,t])},e.prototype.overrideProvider=function(e,t){this.overrideProviderImpl(e,t)},e.prototype.overrideProviderImpl=function(e,r,n){void 0===n&&(n=!1);var o=null;"string"!=typeof e&&(o=t.ɵgetInjectableDef(e))&&"root"===o.providedIn&&this._rootProviderOverrides.push(r.useFactory?{provide:e,useFactory:r.useFactory,deps:r.deps||[]}:{provide:e,useValue:r.useValue});var i,s=0;r.useFactory?(s|=1024,i=r.useFactory):(s|=256,i=r.useValue);var u=(r.deps||[]).map((function(e){var r,n=0;return Array.isArray(e)?e.forEach((function(e){e instanceof t.Optional?n|=2:e instanceof t.SkipSelf?n|=1:r=e})):r=e,[n,r]}));t.ɵoverrideProvider({token:e,flags:s,deps:u,value:i,deprecatedBehavior:n})},e.prototype.overrideTemplateUsingTestingModule=function(e,r){this._assertNotInstantiated("overrideTemplateUsingTestingModule","override template");var n=M([t.Component({selector:"empty",template:r,jit:!0})],(function n(){}));this._templateOverrides.push({component:e,templateOf:n})},e.prototype.createComponent=function(e){var r=this;this._initIfNeeded();var n=this._compiler.getComponentFactory(e);if(!n)throw new Error("Cannot create the component "+t.ɵstringify(e)+" as it was not imported into the testing module!");var o=this.inject(S,!1),s=this.inject(D,!1),u=o?null:this.inject(t.NgZone,null),c=this.inject(P),l="root"+ce++;c.insertRootElement(l);var a=function(){var e=n.create(t.Injector.NULL,[],"#"+l,r._moduleRef);return new i(e,u,s)},p=u?u.run(a):a();return this._activeFixtures.push(p),p},e}(),ae=t.ɵivyEnabled?re:le,pe=t.ɵivyEnabled?ne:de;function de(){return ie=ie||new le}function he(e,t){var r=pe();return e.indexOf(E)>=0?function(){var n=this;return r.compileComponents().then((function(){var o=r.inject(E);return r.execute(e,t,n),o.promise}))}:function(){return r.execute(e,t,this)}}var fe=function(){function e(e){this._moduleDef=e}return e.prototype._addModule=function(){var e=this._moduleDef();e&&pe().configureTestingModule(e)},e.prototype.inject=function(e,t){var r=this;return function(){return r._addModule(),he(e,t).call(this)}},e}(),ve="undefined"==typeof window?global:window;ve.beforeEach&&ve.beforeEach((function(){ae.resetTestingModule(),m()})),
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
e.ComponentFixture=i,e.ComponentFixtureAutoDetect=D,e.ComponentFixtureNoNgZone=S,e.InjectSetupWrapper=fe,e.TestBed=ae,e.TestComponentRenderer=P,e.__core_private_testing_placeholder__="",e.async=function me(e){var t="undefined"!=typeof Zone?Zone:null;if(!t)return function(){return Promise.reject("Zone is needed for the async() test helper but could not be found. Please make sure that your environment includes zone.js/dist/zone.js")};var r=t&&t[t.__symbol__("asyncTest")];return"function"==typeof r?r(e):function i(e){return n.jasmine?function(t){t||((t=function(){}).fail=function(e){throw e}),o(e,this,t,(function(e){if("string"==typeof e)return t.fail(new Error(e));t.fail(e)}))}:function(){var t=this;return new Promise((function(r,n){o(e,t,r,n)}))}}(e)},e.discardPeriodicTasks=function ye(){if(v)return v.discardPeriodicTasks();!function e(){d().pendingPeriodicTimers.length=0}()},e.fakeAsync=function ge(e){return v?v.fakeAsync(e):function t(e){return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n=c.assertPresent();if(p)throw new Error("fakeAsync() calls can not be nested");p=!0;try{if(!l){if(n.getDelegate()instanceof u)throw new Error("fakeAsync() calls can not be nested");l=new u}var o=void 0,i=n.getDelegate();n.setDelegate(l);try{o=e.apply(this,t),h()}finally{n.setDelegate(i)}if(l.pendingPeriodicTimers.length>0)throw new Error(l.pendingPeriodicTimers.length+" periodic timer(s) still in the queue.");if(l.pendingTimers.length>0)throw new Error(l.pendingTimers.length+" timer(s) still in the queue.");return o}finally{p=!1,a()}}}(e)},e.flush=function _e(e){return v?v.flush(e):function t(e){return d().flush(e)}(e)},e.flushMicrotasks=function Me(){return v?v.flushMicrotasks():h()},e.getTestBed=pe,e.inject=he,e.resetFakeAsyncZone=m,e.tick=function Oe(e){return void 0===e&&(e=0),v?v.tick(e):function t(e){void 0===e&&(e=0),d().tick(e)}(e)},e.withModule=function be(e,t){return t?function(){var r=pe();return e&&r.configureTestingModule(e),t.apply(this)}:new fe((function(){return e}))}
/**
     * @license
     * Copyright Google Inc. All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */,e.ɵMetadataOverrider=x,e.ɵTestingCompiler=se,e.ɵTestingCompilerFactory=ue,e.ɵangular_packages_core_testing_testing_a=le,e.ɵangular_packages_core_testing_testing_b=re,e.ɵangular_packages_core_testing_testing_c=ne,Object.defineProperty(e,"__esModule",{value:!0})}));