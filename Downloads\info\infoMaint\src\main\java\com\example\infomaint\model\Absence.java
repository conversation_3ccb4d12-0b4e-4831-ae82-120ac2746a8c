package com.example.infomaint.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.DBRef;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "absences")
public class Absence {
    
    @Id
    private String id;
    
    @DBRef
    @NotNull(message = "User is required")
    private User user;
    
    @NotNull(message = "Absence type is required")
    private AbsenceType type;
    
    @NotNull(message = "Start date is required")
    private LocalDate startDate;
    
    @NotNull(message = "End date is required")
    private LocalDate endDate;
    
    @Positive(message = "Days count must be positive")
    private Integer daysCount;
    
    private String reason;
    
    private String description;
    
    @NotNull(message = "Status is required")
    private AbsenceStatus status = AbsenceStatus.PENDING;
    
    @DBRef
    private User approvedBy;
    
    private LocalDateTime approvedDate;
    
    private String approvalComments;
    
    private String rejectionReason;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
    
    private String createdBy;
    
    private String updatedBy;
    
    // Medical certificate details for sick leave
    private Boolean medicalCertificateRequired = false;
    
    private Boolean medicalCertificateProvided = false;
    
    private String medicalCertificateNumber;
    
    // Emergency contact for urgent absences
    private String emergencyContact;
    
    private String emergencyPhone;
    
    // Replacement details
    private String replacementArrangements;
    
    @DBRef
    private User replacementUser;
    
    public enum AbsenceType {
        ANNUAL_LEAVE, SICK_LEAVE, MATERNITY_LEAVE, PATERNITY_LEAVE, 
        COMPASSIONATE_LEAVE, UNPAID_LEAVE, STUDY_LEAVE, OTHER
    }
    
    public enum AbsenceStatus {
        PENDING, APPROVED, REJECTED, CANCELLED
    }
    
    // Calculate days count based on start and end dates
    public void calculateDaysCount() {
        if (startDate != null && endDate != null) {
            this.daysCount = (int) java.time.temporal.ChronoUnit.DAYS.between(startDate, endDate) + 1;
        }
    }
}
