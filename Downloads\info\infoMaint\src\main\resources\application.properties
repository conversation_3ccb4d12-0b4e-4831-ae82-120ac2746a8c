# Application Configuration
spring.application.name=infoMaint
server.port=8089

# MongoDB Configuration
spring.data.mongodb.host=localhost
spring.data.mongodb.port=27017
spring.data.mongodb.database=infomaint

# JSON Configuration
spring.jackson.default-property-inclusion=NON_NULL

# JWT Configuration
app.jwt.secret=mySecretKey123456789012345678901234567890
app.jwt.expiration=86400000

# CORS Configuration
app.cors.allowed-origins=http://localhost:4200

# Logging
logging.level.com.example.infomaint=DEBUG
logging.level.org.springframework.security=DEBUG
